import { MovieCard } from "./movie-card"

export function RelatedMovies() {
  const relatedMovies = [
    {
      id: 1,
      title: "Spider-Man: Homecoming",
      genre: "Action, Adventure",
      rating: 7.4,
      duration: "2h 13min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 2,
      title: "Spider-Man: Far From Home",
      genre: "Action, Adventure",
      rating: 7.4,
      duration: "2h 9min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 3,
      title: "Venom: Let There Be Carnage",
      genre: "Action, Sci-Fi",
      rating: 5.9,
      duration: "1h 37min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 4,
      title: "The Amazing Spider-Man",
      genre: "Action, Adventure",
      rating: 6.9,
      duration: "2h 16min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
  ]

  return (
    <section className="space-y-6">
      <h2 className="text-3xl font-bold">You Might Also Like</h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {relatedMovies.map((movie) => (
          <MovieCard key={movie.id} movie={movie} />
        ))}
      </div>
    </section>
  )
}
