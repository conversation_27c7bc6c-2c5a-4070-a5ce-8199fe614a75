import { apiService } from "./api"
import { Booking, CreateBookingRequest, PaginationParams, PaginatedResponse } from '@/types/api'

export const bookingService = {
  async createBooking(bookingData: CreateBookingRequest): Promise<Booking> {
    return apiService.post<Booking>("/bookings", bookingData)
  },

  async getBookingById(id: number): Promise<Booking> {
    return apiService.get<Booking>(`/bookings/${id}`)
  },

  async getBookingByReference(reference: string): Promise<Booking> {
    return apiService.get<Booking>(`/bookings/reference/${reference}`)
  },

  async getUserBookings(params: PaginationParams = {}): Promise<PaginatedResponse<Booking>> {
    const queryParams = new URLSearchParams()

    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/bookings/user${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<PaginatedResponse<Booking>>(endpoint)
  },

  async cancelBooking(bookingId: number): Promise<Booking> {
    return apiService.put<Booking>(`/bookings/${bookingId}/cancel`)
  },

  async confirmBooking(bookingId: number): Promise<Booking> {
    return apiService.put<Booking>(`/bookings/${bookingId}/confirm`)
  },

  // Admin endpoints
  async getAllBookings(params: PaginationParams = {}): Promise<PaginatedResponse<Booking>> {
    const queryParams = new URLSearchParams()

    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/bookings/admin/all${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<PaginatedResponse<Booking>>(endpoint)
  },

  async getRevenue(startDate: string, endDate: string): Promise<number> {
    const queryParams = new URLSearchParams()
    queryParams.append('startDate', startDate)
    queryParams.append('endDate', endDate)

    return apiService.get<number>(`/bookings/admin/revenue?${queryParams}`)
  }
}
