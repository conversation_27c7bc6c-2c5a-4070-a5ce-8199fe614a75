import { apiService } from "./api"

export interface Booking {
  id: string
  movieId: number
  showtimeId: number
  seats: string[]
  customerName: string
  customerEmail: string
  customerPhone: string
  totalAmount: number
  bookingDate: string
  status: "confirmed" | "cancelled" | "completed"
}

export interface CreateBookingData {
  showtimeId: number
  seats: string[]
  customerName: string
  customerEmail: string
  customerPhone: string
}

export const bookingService = {
  async createBooking(bookingData: CreateBookingData): Promise<Booking> {
    return apiService.post("/bookings", bookingData)
  },

  async getBookingById(id: string): Promise<Booking> {
    return apiService.get(`/bookings/${id}`)
  },

  async getUserBookings(userId: number): Promise<Booking[]> {
    return apiService.get(`/users/${userId}/bookings`)
  },

  async cancelBooking(bookingId: string): Promise<void> {
    return apiService.delete(`/bookings/${bookingId}`)
  },

  async holdSeats(showtimeId: number, seats: string[]): Promise<{ holdId: string; expiresAt: string }> {
    return apiService.post(`/showtimes/${showtimeId}/hold-seats`, { seats })
  },

  async releaseSeats(showtimeId: number, seats: string[]): Promise<void> {
    return apiService.post(`/showtimes/${showtimeId}/release-seats`, { seats })
  },

  async getBookingHistory(
    userId: number,
    page = 1,
  ): Promise<{
    bookings: Booking[]
    totalPages: number
    currentPage: number
  }> {
    return apiService.get(`/users/${userId}/bookings/history?page=${page}`)
  },
}
