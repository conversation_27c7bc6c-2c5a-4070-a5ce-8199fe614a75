import { apiService } from './api'
import { Showtime, ShowtimeQueryParams, PaginationParams, PaginatedResponse } from '@/types/api'

export const showtimeService = {
  async getAllShowtimes(params: ShowtimeQueryParams = {}): Promise<Showtime[]> {
    const queryParams = new URLSearchParams()
    
    if (params.date) queryParams.append('date', params.date)
    if (params.movieId !== undefined) queryParams.append('movieId', params.movieId.toString())
    if (params.theaterId !== undefined) queryParams.append('theaterId', params.theaterId.toString())

    const endpoint = `/showtimes${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<Showtime[]>(endpoint)
  },

  async getShowtimeById(id: number): Promise<Showtime> {
    return apiService.get<Showtime>(`/showtimes/${id}`)
  },

  async getShowtimeAvailability(id: number): Promise<{
    availableSeats: number
    totalSeats: number
    seatMap: any // This would be the seat availability data
  }> {
    return apiService.get(`/showtimes/${id}/availability`)
  },

  async getShowtimesByMovie(movieId: number, params: PaginationParams = {}): Promise<PaginatedResponse<Showtime>> {
    const queryParams = new URLSearchParams()
    
    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/showtimes/movie/${movieId}${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<PaginatedResponse<Showtime>>(endpoint)
  },

  async getShowtimesByTheater(theaterId: number, params: PaginationParams = {}): Promise<PaginatedResponse<Showtime>> {
    const queryParams = new URLSearchParams()
    
    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/showtimes/theater/${theaterId}${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<PaginatedResponse<Showtime>>(endpoint)
  },

  async getUpcomingShowtimes(): Promise<Showtime[]> {
    return apiService.get<Showtime[]>('/showtimes/upcoming')
  },

  async getTodayShowtimes(): Promise<Showtime[]> {
    return apiService.get<Showtime[]>('/showtimes/today')
  }
}
