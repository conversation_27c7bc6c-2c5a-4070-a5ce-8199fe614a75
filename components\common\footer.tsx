import Link from "next/link"
import { Film, Mail, Phone, MapPin } from "lucide-react"

export function Footer() {
  const footerSections = [
    {
      title: "Movies",
      links: [
        { label: "Now Playing", href: "/movies?filter=now-playing" },
        { label: "Coming Soon", href: "/movies?filter=coming-soon" },
        { label: "Top Rated", href: "/movies?filter=top-rated" },
        { label: "Genres", href: "/movies/genres" },
      ],
    },
    {
      title: "Theaters",
      links: [
        { label: "Find Theaters", href: "/theaters" },
        { label: "Premium Screens", href: "/theaters?type=premium" },
        { label: "IMAX Theaters", href: "/theaters?type=imax" },
        { label: "Drive-in Theaters", href: "/theaters?type=drive-in" },
      ],
    },
    {
      title: "Support",
      links: [
        { label: "Help Center", href: "/help" },
        { label: "Contact Us", href: "/contact" },
        { label: "Refund Policy", href: "/refund-policy" },
        { label: "Terms of Service", href: "/terms" },
      ],
    },
    {
      title: "Account",
      links: [
        { label: "My Profile", href: "/profile" },
        { label: "My Bookings", href: "/dashboard" },
        { label: "Preferences", href: "/preferences" },
        { label: "Notifications", href: "/notifications" },
      ],
    },
  ]

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1 space-y-4">
            <Link href="/" className="flex items-center gap-2 font-bold text-xl">
              <Film className="w-8 h-8 text-red-600" />
              <span>
                Cinema<span className="text-red-600">Book</span>
              </span>
            </Link>
            <p className="text-gray-400 text-sm">
              Your ultimate destination for booking movie tickets online. Experience cinema like never before.
            </p>
            <div className="space-y-2 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>123 Cinema Street, Movie City</span>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-4">
              <h3 className="font-semibold text-lg">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <Link href={link.href} className="text-gray-400 hover:text-white transition-colors text-sm">
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-sm">© 2024 CinemaBook. All rights reserved.</p>
            <div className="flex gap-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
