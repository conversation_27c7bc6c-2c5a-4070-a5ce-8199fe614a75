"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Clock, MapPin, Calendar, Users } from "lucide-react"
import Image from "next/image"
import { useRouter } from "next/navigation"

export function BookingSummary() {
  const router = useRouter()

  const bookingDetails = {
    movie: {
      title: "Spider-Man: No Way Home",
      poster: "/placeholder.svg?height=120&width=80",
      rating: "PG-13",
      duration: "2h 28min",
    },
    theater: {
      name: "PVR Cinemas",
      location: "Phoenix Mall, Bangalore",
      screen: "Screen 3",
    },
    showtime: {
      date: "Today, Dec 25",
      time: "7:30 PM",
    },
    seats: ["F7", "F8"],
    pricing: {
      tickets: 2,
      basePrice: 300,
      convenienceFee: 30,
      taxes: 33,
      total: 363,
    },
  }

  const handleProceedToPayment = () => {
    router.push("/booking/payment")
  }

  return (
    <Card className="sticky top-4">
      <CardHeader>
        <CardTitle>Booking Summary</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Movie Info */}
        <div className="flex gap-3">
          <Image
            src={bookingDetails.movie.poster || "/placeholder.svg"}
            alt={bookingDetails.movie.title}
            width={60}
            height={90}
            className="rounded-lg"
          />
          <div className="flex-1 space-y-1">
            <h3 className="font-semibold leading-tight">{bookingDetails.movie.title}</h3>
            <p className="text-sm text-muted-foreground">{bookingDetails.movie.rating}</p>
            <p className="text-sm text-muted-foreground flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {bookingDetails.movie.duration}
            </p>
          </div>
        </div>

        <Separator />

        {/* Theater & Showtime */}
        <div className="space-y-3">
          <div className="flex items-start gap-2">
            <MapPin className="w-4 h-4 mt-0.5 text-muted-foreground" />
            <div>
              <p className="font-medium">{bookingDetails.theater.name}</p>
              <p className="text-sm text-muted-foreground">{bookingDetails.theater.location}</p>
              <p className="text-sm text-muted-foreground">{bookingDetails.theater.screen}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">{bookingDetails.showtime.date}</span>
            <span className="text-muted-foreground">at</span>
            <span className="font-medium">{bookingDetails.showtime.time}</span>
          </div>
        </div>

        <Separator />

        {/* Selected Seats */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">Selected Seats</span>
          </div>
          <div className="flex gap-2">
            {bookingDetails.seats.map((seat) => (
              <span key={seat} className="px-2 py-1 bg-red-100 text-red-700 rounded text-sm font-medium">
                {seat}
              </span>
            ))}
          </div>
        </div>

        <Separator />

        {/* Pricing */}
        <div className="space-y-3">
          <h4 className="font-medium">Price Breakdown</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>
                Tickets ({bookingDetails.pricing.tickets}x ₹{bookingDetails.pricing.basePrice / 2})
              </span>
              <span>₹{bookingDetails.pricing.basePrice}</span>
            </div>
            <div className="flex justify-between">
              <span>Convenience Fee</span>
              <span>₹{bookingDetails.pricing.convenienceFee}</span>
            </div>
            <div className="flex justify-between">
              <span>Taxes</span>
              <span>₹{bookingDetails.pricing.taxes}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-semibold text-base">
              <span>Total Amount</span>
              <span>₹{bookingDetails.pricing.total}</span>
            </div>
          </div>
        </div>

        <Button className="w-full bg-red-600 hover:bg-red-700" size="lg" onClick={handleProceedToPayment}>
          Proceed to Payment
        </Button>

        <p className="text-xs text-center text-muted-foreground">By proceeding, you agree to our Terms & Conditions</p>
      </CardContent>
    </Card>
  )
}
