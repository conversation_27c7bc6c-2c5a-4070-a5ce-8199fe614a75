import { apiService } from './api'
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User,
  ChangePasswordRequest 
} from '@/types/api'

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/login', credentials)
    
    // Store tokens
    apiService.setAuthTokens(response.token, response.refreshToken)
    
    // Store user data
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify({
        id: response.id,
        email: response.email,
        firstName: response.firstName,
        lastName: response.lastName,
        role: response.role,
        preferredGenre: response.preferredGenre,
        preferredTheaterId: response.preferredTheaterId,
      }))
    }
    
    return response
  },

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/register', userData)
    
    // Store tokens
    apiService.setAuthTokens(response.token, response.refreshToken)
    
    // Store user data
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify({
        id: response.id,
        email: response.email,
        firstName: response.firstName,
        lastName: response.lastName,
        role: response.role,
        preferredGenre: response.preferredGenre,
        preferredTheaterId: response.preferredTheaterId,
      }))
    }
    
    return response
  },

  async validateToken(): Promise<boolean> {
    try {
      return await apiService.get<boolean>('/auth/validate-token')
    } catch (error) {
      return false
    }
  },

  async getUserInfo(): Promise<string> {
    return apiService.get<string>('/auth/user-info')
  },

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refreshToken') 
      : null
    
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await apiService.post<AuthResponse>('/auth/refresh-token', refreshToken)
    
    // Update stored tokens
    apiService.setAuthTokens(response.token, response.refreshToken)
    
    return response
  },

  logout(): void {
    apiService.clearAuth()
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
  },

  getCurrentUser(): User | null {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('user')
      return userData ? JSON.parse(userData) : null
    }
    return null
  },

  isAuthenticated(): boolean {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken')
      const user = localStorage.getItem('user')
      return !!(token && user)
    }
    return false
  }
}
