import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, Calendar, Clock, MapPin, Ticket, Star, Settings, CreditCard, Bell, Film, TrendingUp } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export function UserDashboard() {
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    avatar: "/placeholder.svg?height=80&width=80",
    memberSince: "January 2023",
    totalBookings: 24,
    favoriteGenre: "Action",
  }

  const upcomingBookings = [
    {
      id: "BK2024001234",
      movie: {
        title: "Spider-Man: No Way Home",
        poster: "/placeholder.svg?height=80&width=60",
        rating: 8.4,
      },
      theater: "PVR Cinemas - Phoenix Mall",
      date: "Dec 25, 2024",
      time: "7:30 PM",
      seats: ["F7", "F8"],
      status: "confirmed",
    },
    {
      id: "BK2024001235",
      movie: {
        title: "Dune: Part Two",
        poster: "/placeholder.svg?height=80&width=60",
        rating: 8.8,
      },
      theater: "INOX - Forum Mall",
      date: "Dec 28, 2024",
      time: "9:15 PM",
      seats: ["G5", "G6"],
      status: "confirmed",
    },
  ]

  const bookingHistory = [
    {
      id: "BK2024001230",
      movie: {
        title: "The Batman",
        poster: "/placeholder.svg?height=80&width=60",
        rating: 7.8,
      },
      theater: "Cinepolis - Orion Mall",
      date: "Dec 15, 2024",
      time: "6:00 PM",
      seats: ["E8", "E9"],
      status: "completed",
      amount: 450,
    },
    {
      id: "BK2024001225",
      movie: {
        title: "Top Gun: Maverick",
        poster: "/placeholder.svg?height=80&width=60",
        rating: 8.3,
      },
      theater: "PVR Cinemas - Phoenix Mall",
      date: "Dec 10, 2024",
      time: "8:45 PM",
      seats: ["D5", "D6", "D7"],
      status: "completed",
      amount: 675,
    },
    {
      id: "BK2024001220",
      movie: {
        title: "Avatar: The Way of Water",
        poster: "/placeholder.svg?height=80&width=60",
        rating: 7.6,
      },
      theater: "INOX - Forum Mall",
      date: "Dec 5, 2024",
      time: "7:00 PM",
      seats: ["F10", "F11"],
      status: "completed",
      amount: 520,
    },
  ]

  const favoriteTheaters = [
    { name: "PVR Cinemas - Phoenix Mall", visits: 8, distance: "2.5 km" },
    { name: "INOX - Forum Mall", visits: 6, distance: "3.8 km" },
    { name: "Cinepolis - Orion Mall", visits: 4, distance: "5.2 km" },
  ]

  const stats = [
    { label: "Total Bookings", value: user.totalBookings, icon: Ticket },
    { label: "Movies Watched", value: 18, icon: Film },
    { label: "Favorite Genre", value: user.favoriteGenre, icon: Star },
    { label: "Member Since", value: user.memberSince, icon: Calendar },
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">My Dashboard</h1>
            <p className="text-muted-foreground">Manage your bookings and preferences</p>
          </div>
          <Link href="/dashboard/profile">
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>

        {/* User Profile Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-6">
              <Avatar className="w-20 h-20">
                <AvatarImage src={user.avatar || "/placeholder.svg"} />
                <AvatarFallback className="text-xl">
                  {user.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h2 className="text-2xl font-bold">{user.name}</h2>
                <p className="text-muted-foreground">{user.email}</p>
                <p className="text-muted-foreground">{user.phone}</p>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-2">
                      <stat.icon className="w-5 h-5 text-red-600" />
                    </div>
                    <p className="text-lg font-bold">{stat.value}</p>
                    <p className="text-xs text-muted-foreground">{stat.label}</p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-4 gap-4">
          <Link href="/movies">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Film className="w-8 h-8 mx-auto mb-3 text-red-600" />
                <h3 className="font-semibold">Book Tickets</h3>
                <p className="text-sm text-muted-foreground">Find and book movies</p>
              </CardContent>
            </Card>
          </Link>
          <Link href="/dashboard/profile">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <User className="w-8 h-8 mx-auto mb-3 text-red-600" />
                <h3 className="font-semibold">Profile</h3>
                <p className="text-sm text-muted-foreground">Update your details</p>
              </CardContent>
            </Card>
          </Link>
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <CreditCard className="w-8 h-8 mx-auto mb-3 text-red-600" />
              <h3 className="font-semibold">Payment Methods</h3>
              <p className="text-sm text-muted-foreground">Manage cards</p>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Bell className="w-8 h-8 mx-auto mb-3 text-red-600" />
              <h3 className="font-semibold">Notifications</h3>
              <p className="text-sm text-muted-foreground">Manage alerts</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="upcoming" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upcoming">Upcoming Shows</TabsTrigger>
            <TabsTrigger value="history">Booking History</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
          </TabsList>

          {/* Upcoming Bookings */}
          <TabsContent value="upcoming" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Shows</CardTitle>
              </CardHeader>
              <CardContent>
                {upcomingBookings.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingBookings.map((booking) => (
                      <div key={booking.id} className="border rounded-lg p-4">
                        <div className="flex gap-4">
                          <Image
                            src={booking.movie.poster || "/placeholder.svg"}
                            alt={booking.movie.title}
                            width={60}
                            height={80}
                            className="rounded-lg"
                          />
                          <div className="flex-1 space-y-2">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-semibold text-lg">{booking.movie.title}</h3>
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                  <span>{booking.movie.rating}</span>
                                </div>
                              </div>
                              <Badge className="bg-green-100 text-green-700">
                                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                              </Badge>
                            </div>
                            <div className="grid md:grid-cols-2 gap-4 text-sm">
                              <div className="space-y-1">
                                <p className="flex items-center gap-2">
                                  <MapPin className="w-3 h-3" />
                                  {booking.theater}
                                </p>
                                <p className="flex items-center gap-2">
                                  <Calendar className="w-3 h-3" />
                                  {booking.date}
                                </p>
                              </div>
                              <div className="space-y-1">
                                <p className="flex items-center gap-2">
                                  <Clock className="w-3 h-3" />
                                  {booking.time}
                                </p>
                                <p className="flex items-center gap-2">
                                  <Ticket className="w-3 h-3" />
                                  Seats: {booking.seats.join(", ")}
                                </p>
                              </div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <Button size="sm" variant="outline">
                                View Ticket
                              </Button>
                              <Button size="sm" variant="outline">
                                Download
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Ticket className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="font-semibold mb-2">No upcoming shows</h3>
                    <p className="text-muted-foreground mb-4">Book your next movie experience</p>
                    <Link href="/movies">
                      <Button>Browse Movies</Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Booking History */}
          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Booking History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bookingHistory.map((booking) => (
                    <div key={booking.id} className="border rounded-lg p-4">
                      <div className="flex gap-4">
                        <Image
                          src={booking.movie.poster || "/placeholder.svg"}
                          alt={booking.movie.title}
                          width={60}
                          height={80}
                          className="rounded-lg"
                        />
                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-semibold text-lg">{booking.movie.title}</h3>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                <span>{booking.movie.rating}</span>
                              </div>
                            </div>
                            <div className="text-right">
                              <Badge variant="outline">Completed</Badge>
                              <p className="text-sm font-semibold mt-1">₹{booking.amount}</p>
                            </div>
                          </div>
                          <div className="grid md:grid-cols-2 gap-4 text-sm">
                            <div className="space-y-1">
                              <p className="flex items-center gap-2">
                                <MapPin className="w-3 h-3" />
                                {booking.theater}
                              </p>
                              <p className="flex items-center gap-2">
                                <Calendar className="w-3 h-3" />
                                {booking.date}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="flex items-center gap-2">
                                <Clock className="w-3 h-3" />
                                {booking.time}
                              </p>
                              <p className="flex items-center gap-2">
                                <Ticket className="w-3 h-3" />
                                Seats: {booking.seats.join(", ")}
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-2 pt-2">
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                            <Button size="sm" variant="outline">
                              Book Again
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preferences */}
          <TabsContent value="preferences" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Favorite Theaters</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {favoriteTheaters.map((theater, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{theater.name}</p>
                          <p className="text-sm text-muted-foreground">{theater.distance} away</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{theater.visits} visits</p>
                          <div className="flex items-center gap-1">
                            <TrendingUp className="w-3 h-3 text-green-500" />
                            <span className="text-xs text-green-600">Frequent</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Movie Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Preferred Genres</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {["Action", "Comedy", "Drama", "Sci-Fi", "Thriller"].map((genre) => (
                        <Badge key={genre} variant={genre === "Action" ? "default" : "outline"}>
                          {genre}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Preferred Languages</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {["English", "Hindi", "Tamil"].map((language) => (
                        <Badge key={language} variant={language === "English" ? "default" : "outline"}>
                          {language}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Preferred Show Times</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {["Evening", "Night"].map((time) => (
                        <Badge key={time} variant="outline">
                          {time}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
