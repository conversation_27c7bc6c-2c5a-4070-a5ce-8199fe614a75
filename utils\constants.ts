export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    LOGOUT: "/auth/logout",
    REFRESH: "/auth/refresh",
    PROFILE: "/auth/profile",
  },
  MOVIES: {
    ALL: "/movies",
    BY_ID: (id: number) => `/movies/${id}`,
    SEARCH: "/movies/search",
    FEATURED: "/movies/featured",
    NOW_PLAYING: "/movies/now-playing",
    COMING_SOON: "/movies/coming-soon",
    GENRES: "/movies/genres",
    REVIEWS: (id: number) => `/movies/${id}/reviews`,
  },
  THEATERS: {
    ALL: "/theaters",
    BY_ID: (id: number) => `/theaters/${id}`,
    NEARBY: "/theaters/nearby",
  },
  SHOWTIMES: {
    ALL: "/showtimes",
    BY_MOVIE: (movieId: number) => `/movies/${movieId}/showtimes`,
    BY_THEATER: (theaterId: number) => `/theaters/${theaterId}/showtimes`,
    SEATS: (showtimeId: number) => `/showtimes/${showtimeId}/seats`,
  },
  BOOKINGS: {
    CREATE: "/bookings",
    BY_ID: (id: string) => `/bookings/${id}`,
    USER_BOOKINGS: (userId: number) => `/users/${userId}/bookings`,
    CANCEL: (id: string) => `/bookings/${id}/cancel`,
  },
} as const

export const SEAT_TYPES = {
  REGULAR: { name: "Regular", price: 150, color: "bg-green-200 border-green-400" },
  PREMIUM: { name: "Premium", price: 200, color: "bg-purple-200 border-purple-400" },
  VIP: { name: "VIP", price: 300, color: "bg-yellow-200 border-yellow-400" },
} as const

export const SEAT_STATUS = {
  AVAILABLE: "available",
  OCCUPIED: "occupied",
  SELECTED: "selected",
  WHEELCHAIR: "wheelchair",
} as const

export const MOVIE_GENRES = [
  "Action",
  "Adventure",
  "Animation",
  "Comedy",
  "Crime",
  "Documentary",
  "Drama",
  "Family",
  "Fantasy",
  "Horror",
  "Music",
  "Mystery",
  "Romance",
  "Sci-Fi",
  "Thriller",
  "War",
  "Western",
] as const

export const LANGUAGES = ["English", "Hindi", "Tamil", "Telugu", "Malayalam", "Kannada", "Bengali"] as const

export const MOVIE_FORMATS = ["2D", "3D", "IMAX", "4DX", "Dolby Atmos"] as const

export const TIME_SLOTS = {
  MORNING: { label: "Morning", range: "6:00 AM - 12:00 PM" },
  AFTERNOON: { label: "Afternoon", range: "12:00 PM - 6:00 PM" },
  EVENING: { label: "Evening", range: "6:00 PM - 9:00 PM" },
  NIGHT: { label: "Night", range: "9:00 PM - 12:00 AM" },
} as const

export const BOOKING_STATUS = {
  CONFIRMED: "confirmed",
  CANCELLED: "cancelled",
  COMPLETED: "completed",
} as const

export const MAX_SEATS_PER_BOOKING = 6
export const SEAT_HOLD_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds
