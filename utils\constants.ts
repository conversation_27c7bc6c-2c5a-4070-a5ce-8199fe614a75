export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    REFRESH_TOKEN: "/auth/refresh-token",
    VALIDATE_TOKEN: "/auth/validate-token",
    USER_INFO: "/auth/user-info",
  },
  MOVIES: {
    ALL: "/movies",
    BY_ID: (id: number) => `/movies/${id}`,
    SEARCH: "/movies/search",
    BY_GENRE: (genre: string) => `/movies/genre/${genre}`,
    FEATURED: "/movies/featured",
    NOW_PLAYING: "/movies/now-playing",
    UPCOMING: "/movies/upcoming",
    WITH_SHOWTIMES: "/movies/with-showtimes",
  },
  THEATERS: {
    ALL: "/theaters",
    BY_ID: (id: number) => `/theaters/${id}`,
    WITH_SEATS: (id: number) => `/theaters/${id}/seats`,
    THREE_D: "/theaters/3d",
    IMAX: "/theaters/imax",
    BY_CAPACITY: "/theaters/capacity",
    WITH_SHOWTIMES: "/theaters/with-showtimes",
    BY_MOVIE: (movieId: number) => `/theaters/movie/${movieId}`,
  },
  SHOWTIMES: {
    ALL: "/showtimes",
    BY_ID: (id: number) => `/showtimes/${id}`,
    AVAILABILITY: (id: number) => `/showtimes/${id}/availability`,
    BY_MOVIE: (movieId: number) => `/showtimes/movie/${movieId}`,
    BY_THEATER: (theaterId: number) => `/showtimes/theater/${theaterId}`,
    UPCOMING: "/showtimes/upcoming",
    TODAY: "/showtimes/today",
  },
  SEATS: {
    BY_THEATER: (theaterId: number) => `/seats/theater/${theaterId}`,
    BY_SHOWTIME: (showtimeId: number) => `/seats/showtime/${showtimeId}`,
    AVAILABLE: (theaterId: number) => `/seats/theater/${theaterId}/available`,
    BY_ROW: (theaterId: number, row: string) => `/seats/theater/${theaterId}/row/${row}`,
    HOLD: "/seats/hold",
    RELEASE: "/seats/release",
  },
  BOOKINGS: {
    CREATE: "/bookings",
    BY_ID: (id: number) => `/bookings/${id}`,
    BY_REFERENCE: (reference: string) => `/bookings/reference/${reference}`,
    USER_BOOKINGS: "/bookings/user",
    CANCEL: (id: number) => `/bookings/${id}/cancel`,
    CONFIRM: (id: number) => `/bookings/${id}/confirm`,
    ADMIN: {
      ALL: "/bookings/admin/all",
      REVENUE: "/bookings/admin/revenue",
    },
  },
  USERS: {
    PROFILE: "/users/profile",
    PREFERENCES: "/users/preferences",
    PASSWORD: "/users/password",
    BOOKING_HISTORY: "/users/booking-history",
    DELETE_ACCOUNT: "/users/account",
  },
} as const

export const SEAT_TYPES = {
  REGULAR: { name: "Regular", price: 150, color: "bg-green-200 border-green-400" },
  PREMIUM: { name: "Premium", price: 200, color: "bg-purple-200 border-purple-400" },
  VIP: { name: "VIP", price: 300, color: "bg-yellow-200 border-yellow-400" },
} as const

export const SEAT_STATUS = {
  AVAILABLE: "available",
  OCCUPIED: "occupied",
  SELECTED: "selected",
  WHEELCHAIR: "wheelchair",
} as const

export const MOVIE_GENRES = [
  "Action",
  "Adventure",
  "Animation",
  "Comedy",
  "Crime",
  "Documentary",
  "Drama",
  "Family",
  "Fantasy",
  "Horror",
  "Music",
  "Mystery",
  "Romance",
  "Sci-Fi",
  "Thriller",
  "War",
  "Western",
] as const

export const LANGUAGES = ["English", "Hindi", "Tamil", "Telugu", "Malayalam", "Kannada", "Bengali"] as const

export const MOVIE_FORMATS = ["2D", "3D", "IMAX", "4DX", "Dolby Atmos"] as const

export const TIME_SLOTS = {
  MORNING: { label: "Morning", range: "6:00 AM - 12:00 PM" },
  AFTERNOON: { label: "Afternoon", range: "12:00 PM - 6:00 PM" },
  EVENING: { label: "Evening", range: "6:00 PM - 9:00 PM" },
  NIGHT: { label: "Night", range: "9:00 PM - 12:00 AM" },
} as const

export const BOOKING_STATUS = {
  CONFIRMED: "confirmed",
  CANCELLED: "cancelled",
  COMPLETED: "completed",
} as const

export const MAX_SEATS_PER_BOOKING = 6
export const SEAT_HOLD_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds
