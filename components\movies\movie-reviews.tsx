import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, ThumbsUp, ThumbsDown } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function MovieReviews() {
  const reviews = [
    {
      id: 1,
      user: {
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=40&width=40",
        initials: "J<PERSON>",
      },
      rating: 9,
      date: "2 days ago",
      title: "Amazing conclusion to the trilogy!",
      content:
        "This movie exceeded all my expectations. The way they brought together all the previous Spider-Man universes was brilliant. <PERSON> delivers his best performance yet, and the action sequences are spectacular.",
      helpful: 24,
      notHelpful: 2,
    },
    {
      id: 2,
      user: {
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=40&width=40",
        initials: "SJ",
      },
      rating: 8,
      date: "1 week ago",
      title: "Great movie with some minor flaws",
      content:
        "While the movie is entertaining and has great special effects, I felt the pacing was a bit off in the middle. However, the emotional moments really hit hard and the ending was satisfying.",
      helpful: 18,
      notHelpful: 5,
    },
    {
      id: 3,
      user: {
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=40&width=40",
        initials: "MW",
      },
      rating: 10,
      date: "2 weeks ago",
      title: "Perfect superhero movie!",
      content:
        "Everything about this movie is perfect. The story, the acting, the visual effects, and especially the nostalgia factor. It's a love letter to Spider-Man fans everywhere.",
      helpful: 45,
      notHelpful: 1,
    },
  ]

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold">Reviews & Ratings</h2>
        <Button variant="outline">Write a Review</Button>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Rating Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader className="text-center">
              <div className="space-y-2">
                <div className="text-4xl font-bold">8.4</div>
                <div className="flex justify-center">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`w-5 h-5 ${star <= 4 ? "text-yellow-400 fill-current" : "text-gray-300"}`}
                    />
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">Based on 1,234 reviews</p>
              </div>
            </CardHeader>
          </Card>

          {/* Rating Breakdown */}
          <Card>
            <CardHeader>
              <h3 className="font-semibold">Rating Breakdown</h3>
            </CardHeader>
            <CardContent className="space-y-3">
              {[5, 4, 3, 2, 1].map((rating) => (
                <div key={rating} className="flex items-center gap-3">
                  <span className="text-sm w-6">{rating}★</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-400 h-2 rounded-full"
                      style={{
                        width: `${rating === 5 ? 60 : rating === 4 ? 25 : rating === 3 ? 10 : rating === 2 ? 3 : 2}%`,
                      }}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground w-8">
                    {rating === 5 ? 60 : rating === 4 ? 25 : rating === 3 ? 10 : rating === 2 ? 3 : 2}%
                  </span>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Reviews List */}
        <div className="lg:col-span-2 space-y-6">
          {reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Review Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={review.user.avatar || "/placeholder.svg"} />
                        <AvatarFallback>{review.user.initials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold">{review.user.name}</p>
                        <p className="text-sm text-muted-foreground">{review.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${star <= review.rating ? "text-yellow-400 fill-current" : "text-gray-300"}`}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Review Content */}
                  <div>
                    <h4 className="font-semibold mb-2">{review.title}</h4>
                    <p className="text-muted-foreground">{review.content}</p>
                  </div>

                  {/* Review Actions */}
                  <div className="flex items-center gap-4 pt-2">
                    <Button variant="ghost" size="sm" className="text-muted-foreground">
                      <ThumbsUp className="w-4 h-4 mr-1" />
                      Helpful ({review.helpful})
                    </Button>
                    <Button variant="ghost" size="sm" className="text-muted-foreground">
                      <ThumbsDown className="w-4 h-4 mr-1" />
                      Not Helpful ({review.notHelpful})
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          <div className="text-center">
            <Button variant="outline">Load More Reviews</Button>
          </div>
        </div>
      </div>
    </section>
  )
}
