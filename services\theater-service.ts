import { apiService } from './api'
import { Theater, Seat, TheaterQueryParams } from '@/types/api'

export const theaterService = {
  async getAllTheaters(): Promise<Theater[]> {
    return apiService.get<Theater[]>('/theaters')
  },

  async getTheaterById(id: number): Promise<Theater> {
    return apiService.get<Theater>(`/theaters/${id}`)
  },

  async getTheaterWithSeats(id: number): Promise<Theater & { seats: Seat[] }> {
    return apiService.get<Theater & { seats: Seat[] }>(`/theaters/${id}/seats`)
  },

  async get3DTheaters(): Promise<Theater[]> {
    return apiService.get<Theater[]>('/theaters/3d')
  },

  async getIMAXTheaters(): Promise<Theater[]> {
    return apiService.get<Theater[]>('/theaters/imax')
  },

  async getTheatersByCapacity(params: TheaterQueryParams = {}): Promise<Theater[]> {
    const queryParams = new URLSearchParams()
    
    if (params.minSeats !== undefined) queryParams.append('minSeats', params.minSeats.toString())
    if (params.maxSeats !== undefined) queryParams.append('maxSeats', params.maxSeats.toString())

    const endpoint = `/theaters/capacity${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<Theater[]>(endpoint)
  },

  async getTheatersWithShowtimes(date?: string): Promise<Theater[]> {
    const queryParams = new URLSearchParams()
    
    if (date) queryParams.append('date', date)

    const endpoint = `/theaters/with-showtimes${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<Theater[]>(endpoint)
  },

  async getTheatersByMovie(movieId: number): Promise<Theater[]> {
    return apiService.get<Theater[]>(`/theaters/movie/${movieId}`)
  }
}
