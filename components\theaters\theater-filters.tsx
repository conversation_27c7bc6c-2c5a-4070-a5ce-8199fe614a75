import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

export function TheaterFilters() {
  const amenities = [
    "Parking",
    "Food Court",
    "WiFi",
    "Wheelchair Access",
    "Premium Seating",
    "IMAX",
    "4DX",
    "Dolby Atmos",
  ]
  const distances = ["Within 2km", "Within 5km", "Within 10km", "Within 20km"]
  const priceRanges = ["Under ₹200", "₹200-₹400", "₹400-₹600", "Above ₹600"]

  const activeFilters = ["Parking", "IMAX", "Within 5km"]

  return (
    <div className="space-y-6">
      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm font-medium">Active filters:</span>
          {activeFilters.map((filter) => (
            <Badge key={filter} variant="secondary" className="flex items-center gap-1">
              {filter}
              <X className="w-3 h-3 cursor-pointer" />
            </Badge>
          ))}
          <Button variant="ghost" size="sm" className="text-red-600">
            Clear all
          </Button>
        </div>
      )}

      {/* Filter Categories */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="space-y-3">
          <h3 className="font-semibold">Amenities</h3>
          <div className="flex flex-wrap gap-2">
            {amenities.map((amenity) => (
              <Button
                key={amenity}
                variant={activeFilters.includes(amenity) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(amenity) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {amenity}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold">Distance</h3>
          <div className="flex flex-wrap gap-2">
            {distances.map((distance) => (
              <Button
                key={distance}
                variant={activeFilters.includes(distance) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(distance) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {distance}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold">Price Range</h3>
          <div className="flex flex-wrap gap-2">
            {priceRanges.map((range) => (
              <Button
                key={range}
                variant={activeFilters.includes(range) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(range) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {range}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
