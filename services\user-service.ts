import { apiService } from './api'
import { 
  User, 
  UpdateProfileRequest, 
  UpdatePreferencesRequest, 
  ChangePasswordRequest,
  Booking,
  PaginationParams
} from '@/types/api'

export const userService = {
  async getProfile(): Promise<User> {
    return apiService.get<User>('/users/profile')
  },

  async updateProfile(userData: UpdateProfileRequest): Promise<User> {
    const updatedUser = await apiService.put<User>('/users/profile', userData)
    
    // Update stored user data
    if (typeof window !== 'undefined') {
      const currentUser = localStorage.getItem('user')
      if (currentUser) {
        const user = JSON.parse(currentUser)
        const mergedUser = { ...user, ...updatedUser }
        localStorage.setItem('user', JSON.stringify(mergedUser))
      }
    }
    
    return updatedUser
  },

  async updatePreferences(preferences: UpdatePreferencesRequest): Promise<void> {
    await apiService.put<void>('/users/preferences', preferences)
    
    // Update stored user preferences
    if (typeof window !== 'undefined') {
      const currentUser = localStorage.getItem('user')
      if (currentUser) {
        const user = JSON.parse(currentUser)
        const updatedUser = { ...user, ...preferences }
        localStorage.setItem('user', JSON.stringify(updatedUser))
      }
    }
  },

  async changePassword(passwordData: ChangePasswordRequest): Promise<void> {
    return apiService.put<void>('/users/password', passwordData)
  },

  async getBookingHistory(params: PaginationParams = {}): Promise<{
    bookings: Booking[]
    totalPages: number
    currentPage: number
    totalElements: number
  }> {
    const queryParams = new URLSearchParams()
    
    if (params.page !== undefined) queryParams.append('page', params.page.toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/users/booking-history${queryParams.toString() ? `?${queryParams}` : ''}`
    
    const response = await apiService.get<{
      content: Booking[]
      totalPages: number
      number: number
      totalElements: number
    }>(endpoint)

    return {
      bookings: response.content,
      totalPages: response.totalPages,
      currentPage: response.number,
      totalElements: response.totalElements
    }
  },

  async deleteAccount(): Promise<void> {
    await apiService.delete<void>('/users/account')
    
    // Clear all stored data
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      window.location.href = '/auth/login'
    }
  }
}
