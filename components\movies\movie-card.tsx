"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Clock } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface Movie {
  id: number
  title: string
  genre: string
  rating: number
  duration: string
  language: string
  poster: string
}

interface MovieCardProps {
  movie: Movie
}

export function MovieCard({ movie }: MovieCardProps) {
  const router = useRouter()

  const handleBookNow = (e: React.MouseEvent) => {
    e.preventDefault() // Prevent Link navigation
    router.push(`/movies/${movie.id}`)
  }

  return (
    <Link href={`/movies/${movie.id}`}>
      <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
        <div className="relative aspect-[3/4] overflow-hidden">
          <Image
            src={movie.poster || "/placeholder.svg"}
            alt={movie.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-3 right-3">
            <Badge className="bg-yellow-500 text-black">
              <Star className="w-3 h-3 mr-1" />
              {movie.rating}
            </Badge>
          </div>
        </div>

        <CardContent className="p-4 space-y-3">
          <div>
            <h3 className="font-semibold text-lg leading-tight line-clamp-2">{movie.title}</h3>
            <p className="text-sm text-muted-foreground">{movie.genre}</p>
          </div>

          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {movie.duration}
            </span>
            <span>{movie.language}</span>
          </div>

          <Button className="w-full bg-red-600 hover:bg-red-700" onClick={handleBookNow}>
            Book Now
          </Button>
        </CardContent>
      </Card>
    </Link>
  )
}
