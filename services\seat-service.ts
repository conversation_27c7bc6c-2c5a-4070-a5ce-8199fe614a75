import { apiService } from './api'
import { Seat, SeatHoldRequest } from '@/types/api'

export const seatService = {
  async getSeatsByTheater(theaterId: number): Promise<Seat[]> {
    return apiService.get<Seat[]>(`/seats/theater/${theaterId}`)
  },

  async getSeatsByShowtime(showtimeId: number): Promise<Seat[]> {
    return apiService.get<Seat[]>(`/seats/showtime/${showtimeId}`)
  },

  async getAvailableSeats(theaterId: number, showtimeId: number): Promise<Seat[]> {
    const queryParams = new URLSearchParams()
    queryParams.append('showtimeId', showtimeId.toString())

    return apiService.get<Seat[]>(`/seats/theater/${theaterId}/available?${queryParams}`)
  },

  async getSeatsByRow(theaterId: number, rowNumber: string): Promise<Seat[]> {
    return apiService.get<Seat[]>(`/seats/theater/${theaterId}/row/${encodeURIComponent(rowNumber)}`)
  },

  async holdSeats(request: SeatHoldRequest): Promise<boolean> {
    return apiService.post<boolean>('/seats/hold', request)
  },

  async releaseSeats(request: SeatHoldRequest): Promise<boolean> {
    return apiService.post<boolean>('/seats/release', request)
  }
}
