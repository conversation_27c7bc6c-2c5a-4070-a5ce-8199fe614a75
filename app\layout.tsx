import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Navbar } from "@/components/common/navbar"
import { Footer } from "@/components/common/footer"
import { AuthProvider } from "@/context/auth-context"
import { BookingProvider } from "@/context/booking-context"
import { NotificationProvider } from "@/components/common/notification-system"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "CinemaBook - Your Ultimate Movie Booking Experience",
  description:
    "Book movie tickets online with ease. Find showtimes, select seats, and enjoy the best cinema experience.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <NotificationProvider>
          <AuthProvider>
            <BookingProvider>
              <div className="min-h-screen flex flex-col">
                <Navbar />
                <main className="flex-1">{children}</main>
                <Footer />
              </div>
            </BookingProvider>
          </AuthProvider>
        </NotificationProvider>
      </body>
    </html>
  )
}
