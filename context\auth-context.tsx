"use client"

import type React from "react"

import { create<PERSON>ontext, useContext, useEffect, useState } from "react"
import { authService } from '@/services/auth-service'
import { userService } from '@/services/user-service'
import { User, LoginRequest, RegisterRequest, UpdateProfileRequest } from '@/types/api'

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => void
  register: (userData: RegisterRequest) => Promise<void>
  updateProfile: (userData: UpdateProfileRequest) => Promise<void>
  checkAuthStatus: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const checkAuthStatus = async () => {
    try {
      if (authService.isAuthenticated()) {
        const currentUser = authService.getCurrentUser()
        if (currentUser) {
          // Validate token with backend
          const isValid = await authService.validateToken()
          if (isValid) {
            setUser(currentUser)
          } else {
            // Token is invalid, clear auth
            authService.logout()
            setUser(null)
          }
        }
      }
    } catch (error) {
      console.error("Error checking auth status:", error)
      authService.logout()
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (credentials: LoginRequest) => {
    setIsLoading(true)
    try {
      const authResponse = await authService.login(credentials)

      // Set user from auth response
      const userData: User = {
        id: authResponse.id,
        email: authResponse.email,
        firstName: authResponse.firstName,
        lastName: authResponse.lastName,
        phone: '', // Will be fetched from profile later
        role: authResponse.role as 'USER' | 'ADMIN',
        preferredGenre: authResponse.preferredGenre || undefined,
        preferredTheaterId: authResponse.preferredTheaterId || undefined,
        isActive: true,
        createdAt: new Date().toISOString()
      }

      setUser(userData)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterRequest) => {
    setIsLoading(true)
    try {
      const authResponse = await authService.register(userData)

      // Set user from auth response
      const newUser: User = {
        id: authResponse.id,
        email: authResponse.email,
        firstName: authResponse.firstName,
        lastName: authResponse.lastName,
        phone: userData.phone,
        role: authResponse.role as 'USER' | 'ADMIN',
        preferredGenre: authResponse.preferredGenre,
        preferredTheaterId: authResponse.preferredTheaterId,
        isActive: true,
        createdAt: new Date().toISOString()
      }

      setUser(newUser)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const updateProfile = async (userData: UpdateProfileRequest) => {
    setIsLoading(true)
    try {
      const updatedUser = await userService.updateProfile(userData)
      setUser(updatedUser)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    authService.logout()
    setUser(null)
  }

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    register,
    updateProfile,
    checkAuthStatus,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
