// Simple utility to test backend connectivity
export async function testBackendConnection(): Promise<boolean> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'
    const response = await fetch(`${apiUrl}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    return response.ok
  } catch (error) {
    console.error('Backend connection test failed:', error)
    return false
  }
}

export async function testBackendAuth(): Promise<boolean> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'
    const response = await fetch(`${apiUrl}/auth/validate-token`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    // Even if it returns 401, it means the endpoint is accessible
    return response.status === 401 || response.ok
  } catch (error) {
    console.error('Backend auth endpoint test failed:', error)
    return false
  }
}
