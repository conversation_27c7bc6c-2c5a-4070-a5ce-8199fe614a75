import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search, MapPin, Locate } from "lucide-react"

export function TheaterSearch() {
  return (
    <div className="max-w-2xl mx-auto space-y-4">
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input placeholder="Search theaters by name or location..." className="pl-10" />
        </div>
        <Button variant="outline" className="flex items-center gap-2">
          <Locate className="w-4 h-4" />
          Use My Location
        </Button>
      </div>

      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <MapPin className="w-4 h-4" />
        <span>Showing theaters in Bangalore</span>
      </div>
    </div>
  )
}
