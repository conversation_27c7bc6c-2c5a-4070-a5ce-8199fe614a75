import { apiService } from "./api"

export interface Movie {
  id: number
  title: string
  description: string
  poster: string
  backdrop: string
  rating: number
  duration: string
  releaseDate: string
  genre: string[]
  language: string
  director: string
  cast: string[]
  certification: string
}

export interface MovieFilters {
  genre?: string[]
  language?: string[]
  rating?: number
  sortBy?: "popularity" | "rating" | "release_date" | "title"
  sortOrder?: "asc" | "desc"
}

export const movieService = {
  async getAllMovies(
    page = 1,
    filters: MovieFilters = {},
  ): Promise<{
    movies: Movie[]
    totalPages: number
    currentPage: number
    totalMovies: number
  }> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      ...Object.entries(filters).reduce(
        (acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = Array.isArray(value) ? value.join(",") : value.toString()
          }
          return acc
        },
        {} as Record<string, string>,
      ),
    })

    return apiService.get(`/movies?${queryParams}`)
  },

  async getMovieById(id: number): Promise<Movie> {
    return apiService.get(`/movies/${id}`)
  },

  async searchMovies(query: string, filters: MovieFilters = {}): Promise<Movie[]> {
    const queryParams = new URLSearchParams({
      q: query,
      ...Object.entries(filters).reduce(
        (acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = Array.isArray(value) ? value.join(",") : value.toString()
          }
          return acc
        },
        {} as Record<string, string>,
      ),
    })

    return apiService.get(`/movies/search?${queryParams}`)
  },

  async getFeaturedMovies(): Promise<Movie[]> {
    return apiService.get("/movies/featured")
  },

  async getMovieGenres(): Promise<string[]> {
    return apiService.get("/movies/genres")
  },

  async getNowPlayingMovies(): Promise<Movie[]> {
    return apiService.get("/movies/now-playing")
  },

  async getComingSoonMovies(): Promise<Movie[]> {
    return apiService.get("/movies/coming-soon")
  },

  async getMovieReviews(movieId: number): Promise<any[]> {
    return apiService.get(`/movies/${movieId}/reviews`)
  },

  async addMovieReview(movieId: number, review: { rating: number; comment: string }): Promise<any> {
    return apiService.post(`/movies/${movieId}/reviews`, review)
  },
}
