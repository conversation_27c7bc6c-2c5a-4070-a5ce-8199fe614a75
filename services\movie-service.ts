import { apiService } from "./api"
import { Movie, PaginatedResponse, MovieQueryParams, PaginationParams } from '@/types/api'

export const movieService = {
  async getAllMovies(params: PaginationParams = {}): Promise<PaginatedResponse<Movie>> {
    const queryParams = new URLSearchParams()

    // Convert to 0-based page for backend (backend expects 0-based)
    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/movies${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<PaginatedResponse<Movie>>(endpoint)
  },

  async getMovieById(id: number): Promise<Movie> {
    return apiService.get<Movie>(`/movies/${id}`)
  },

  async searchMovies(params: MovieQueryParams): Promise<PaginatedResponse<Movie>> {
    const queryParams = new URLSearchParams()

    if (params.query) queryParams.append('query', params.query)
    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    return apiService.get<PaginatedResponse<Movie>>(`/movies/search?${queryParams}`)
  },

  async getMoviesByGenre(genre: string, params: PaginationParams = {}): Promise<PaginatedResponse<Movie>> {
    const queryParams = new URLSearchParams()

    if (params.page !== undefined) queryParams.append('page', (params.page - 1).toString())
    if (params.size !== undefined) queryParams.append('size', params.size.toString())
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params.sortDir) queryParams.append('sortDir', params.sortDir)

    const endpoint = `/movies/genre/${encodeURIComponent(genre)}${queryParams.toString() ? `?${queryParams}` : ''}`
    return apiService.get<PaginatedResponse<Movie>>(endpoint)
  },

  async getFeaturedMovies(): Promise<Movie[]> {
    return apiService.get<Movie[]>("/movies/featured")
  },

  async getNowPlayingMovies(): Promise<Movie[]> {
    return apiService.get<Movie[]>("/movies/now-playing")
  },

  async getUpcomingMovies(): Promise<Movie[]> {
    return apiService.get<Movie[]>("/movies/upcoming")
  },

  async getMoviesWithShowtimes(): Promise<Movie[]> {
    return apiService.get<Movie[]>("/movies/with-showtimes")
  }
}
