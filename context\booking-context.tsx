"use client"

import type React from "react"

import { createContext, useContext, useState } from "react"
import { Movie, Showtime, Seat, CreateBookingRequest } from '@/types/api'
import { bookingService } from '@/services/booking-service'
import { seatService } from '@/services/seat-service'

interface BookingDetails {
  customerName: string
  customerEmail: string
  customerPhone: string
}

interface BookingContextType {
  selectedMovie: Movie | null
  selectedShowtime: Showtime | null
  selectedSeats: Seat[]
  bookingDetails: BookingDetails | null
  totalPrice: number
  isLoading: boolean
  setSelectedMovie: (movie: Movie) => void
  setSelectedShowtime: (showtime: Showtime) => void
  addSeat: (seat: Seat) => void
  removeSeat: (seatId: number) => void
  updateBookingDetails: (details: BookingDetails) => void
  resetBooking: () => void
  holdSeats: () => Promise<void>
  releaseSeats: () => Promise<void>
  confirmBooking: () => Promise<string>
}

const BookingContext = createContext<BookingContextType | undefined>(undefined)

export function BookingProvider({ children }: { children: React.ReactNode }) {
  const [selectedMovie, setSelectedMovie] = useState<Movie | null>(null)
  const [selectedShowtime, setSelectedShowtime] = useState<Showtime | null>(null)
  const [selectedSeats, setSelectedSeats] = useState<Seat[]>([])
  const [bookingDetails, setBookingDetails] = useState<BookingDetails | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const addSeat = (seat: Seat) => {
    if (selectedSeats.length >= 6) {
      throw new Error("Maximum 6 seats can be selected")
    }
    if (selectedSeats.find((s) => s.id === seat.id)) {
      return // Seat already selected
    }
    setSelectedSeats((prev) => [...prev, seat])
  }

  const removeSeat = (seatId: number) => {
    setSelectedSeats((prev) => prev.filter((seat) => seat.id !== seatId))
  }

  const updateBookingDetails = (details: BookingDetails) => {
    setBookingDetails(details)
  }

  const resetBooking = () => {
    setSelectedMovie(null)
    setSelectedShowtime(null)
    setSelectedSeats([])
    setBookingDetails(null)
  }

  const holdSeats = async () => {
    if (!selectedShowtime || selectedSeats.length === 0) {
      throw new Error("No showtime or seats selected")
    }

    setIsLoading(true)
    try {
      await seatService.holdSeats({
        showtimeId: selectedShowtime.id,
        seatIds: selectedSeats.map(seat => seat.id),
        action: 'HOLD'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const releaseSeats = async () => {
    if (!selectedShowtime || selectedSeats.length === 0) {
      return
    }

    setIsLoading(true)
    try {
      await seatService.releaseSeats({
        showtimeId: selectedShowtime.id,
        seatIds: selectedSeats.map(seat => seat.id),
        action: 'RELEASE'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const confirmBooking = async (): Promise<string> => {
    if (!selectedMovie || !selectedShowtime || selectedSeats.length === 0 || !bookingDetails) {
      throw new Error("Incomplete booking information")
    }

    setIsLoading(true)
    try {
      const bookingRequest: CreateBookingRequest = {
        showtimeId: selectedShowtime.id,
        seatIds: selectedSeats.map(seat => seat.id),
        customerName: bookingDetails.customerName,
        customerEmail: bookingDetails.customerEmail,
        customerPhone: bookingDetails.customerPhone,
      }

      const booking = await bookingService.createBooking(bookingRequest)

      // Reset booking state after confirmation
      resetBooking()

      return booking.bookingReference
    } finally {
      setIsLoading(false)
    }
  }

  const totalPrice = selectedSeats.reduce((total, seat) => total + seat.basePrice, 0)

  const value = {
    selectedMovie,
    selectedShowtime,
    selectedSeats,
    bookingDetails,
    totalPrice,
    isLoading,
    setSelectedMovie,
    setSelectedShowtime,
    addSeat,
    removeSeat,
    updateBookingDetails,
    resetBooking,
    holdSeats,
    releaseSeats,
    confirmBooking,
  }

  return <BookingContext.Provider value={value}>{children}</BookingContext.Provider>
}

export function useBooking() {
  const context = useContext(BookingContext)
  if (context === undefined) {
    throw new Error("useBooking must be used within a BookingProvider")
  }
  return context
}
