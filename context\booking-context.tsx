"use client"

import type React from "react"

import { createContext, useContext, useState } from "react"

interface Movie {
  id: number
  title: string
  poster: string
  rating: number
  duration: string
  genre: string
}

interface Showtime {
  id: number
  movieId: number
  theaterId: number
  date: string
  time: string
  format: string
  price: number
}

interface Seat {
  id: string
  row: string
  number: number
  type: "regular" | "premium" | "vip"
  price: number
  isAvailable: boolean
}

interface BookingDetails {
  customerName: string
  customerEmail: string
  customerPhone: string
}

interface BookingContextType {
  selectedMovie: Movie | null
  selectedShowtime: Showtime | null
  selectedSeats: Seat[]
  bookingDetails: BookingDetails | null
  totalPrice: number
  setSelectedMovie: (movie: Movie) => void
  setSelectedShowtime: (showtime: Showtime) => void
  addSeat: (seat: Seat) => void
  removeSeat: (seatId: string) => void
  updateBookingDetails: (details: BookingDetails) => void
  resetBooking: () => void
  confirmBooking: () => Promise<string>
}

const BookingContext = createContext<BookingContextType | undefined>(undefined)

export function BookingProvider({ children }: { children: React.ReactNode }) {
  const [selectedMovie, setSelectedMovie] = useState<Movie | null>(null)
  const [selectedShowtime, setSelectedShowtime] = useState<Showtime | null>(null)
  const [selectedSeats, setSelectedSeats] = useState<Seat[]>([])
  const [bookingDetails, setBookingDetails] = useState<BookingDetails | null>(null)

  const addSeat = (seat: Seat) => {
    if (selectedSeats.length >= 6) {
      throw new Error("Maximum 6 seats can be selected")
    }
    if (selectedSeats.find((s) => s.id === seat.id)) {
      return // Seat already selected
    }
    setSelectedSeats((prev) => [...prev, seat])
  }

  const removeSeat = (seatId: string) => {
    setSelectedSeats((prev) => prev.filter((seat) => seat.id !== seatId))
  }

  const updateBookingDetails = (details: BookingDetails) => {
    setBookingDetails(details)
  }

  const resetBooking = () => {
    setSelectedMovie(null)
    setSelectedShowtime(null)
    setSelectedSeats([])
    setBookingDetails(null)
  }

  const confirmBooking = async (): Promise<string> => {
    if (!selectedMovie || !selectedShowtime || selectedSeats.length === 0 || !bookingDetails) {
      throw new Error("Incomplete booking information")
    }

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Generate booking ID
    const bookingId = `BK${Date.now()}`

    // Reset booking state after confirmation
    resetBooking()

    return bookingId
  }

  const totalPrice = selectedSeats.reduce((total, seat) => total + seat.price, 0)

  const value = {
    selectedMovie,
    selectedShowtime,
    selectedSeats,
    bookingDetails,
    totalPrice,
    setSelectedMovie,
    setSelectedShowtime,
    addSeat,
    removeSeat,
    updateBookingDetails,
    resetBooking,
    confirmBooking,
  }

  return <BookingContext.Provider value={value}>{children}</BookingContext.Provider>
}

export function useBooking() {
  const context = useContext(BookingContext)
  if (context === undefined) {
    throw new Error("useBooking must be used within a BookingProvider")
  }
  return context
}
