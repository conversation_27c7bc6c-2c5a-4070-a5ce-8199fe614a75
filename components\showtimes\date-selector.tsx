"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

export function DateSelector() {
  const [selectedDate, setSelectedDate] = useState(0)

  const generateDates = () => {
    const dates = []
    for (let i = 0; i < 7; i++) {
      const date = new Date()
      date.setDate(date.getDate() + i)
      dates.push({
        date: date.getDate(),
        day: date.toLocaleDateString("en-US", { weekday: "short" }),
        month: date.toLocaleDateString("en-US", { month: "short" }),
        isToday: i === 0,
      })
    }
    return dates
  }

  const dates = generateDates()

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Select Date</h2>

      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon">
          <ChevronLeft className="w-4 h-4" />
        </Button>

        <div className="flex gap-2 overflow-x-auto">
          {dates.map((dateInfo, index) => (
            <Button
              key={index}
              variant={selectedDate === index ? "default" : "outline"}
              className={`flex-shrink-0 flex flex-col h-auto py-3 px-4 ${
                selectedDate === index ? "bg-red-600 hover:bg-red-700" : ""
              }`}
              onClick={() => setSelectedDate(index)}
            >
              <span className="text-xs">{dateInfo.day}</span>
              <span className="text-lg font-bold">{dateInfo.date}</span>
              <span className="text-xs">{dateInfo.month}</span>
              {dateInfo.isToday && <span className="text-xs text-red-400">Today</span>}
            </Button>
          ))}
        </div>

        <Button variant="outline" size="icon">
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}
