import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

export function MovieCast() {
  const cast = [
    {
      id: 1,
      name: "<PERSON>",
      character: "<PERSON> / Spider-Man",
      image: "/placeholder.svg?height=200&width=150",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      character: "<PERSON><PERSON>",
      image: "/placeholder.svg?height=200&width=150",
    },
    {
      id: 3,
      name: "<PERSON>",
      character: "<PERSON>",
      image: "/placeholder.svg?height=200&width=150",
    },
    {
      id: 4,
      name: "<PERSON>",
      character: "<PERSON>",
      image: "/placeholder.svg?height=200&width=150",
    },
    {
      id: 5,
      name: "<PERSON>",
      character: "<PERSON>",
      image: "/placeholder.svg?height=200&width=150",
    },
    {
      id: 6,
      name: "<PERSON>",
      character: "Green Goblin",
      image: "/placeholder.svg?height=200&width=150",
    },
  ]

  return (
    <section className="space-y-6">
      <h2 className="text-3xl font-bold">Cast & Crew</h2>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
        {cast.map((actor) => (
          <Card key={actor.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="aspect-[3/4] relative">
              <Image src={actor.image || "/placeholder.svg"} alt={actor.name} fill className="object-cover" />
            </div>
            <CardContent className="p-4 text-center">
              <h3 className="font-semibold text-sm leading-tight">{actor.name}</h3>
              <p className="text-xs text-muted-foreground mt-1">{actor.character}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}
