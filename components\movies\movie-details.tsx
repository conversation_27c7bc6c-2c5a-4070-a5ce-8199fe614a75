import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Clock, Calendar, Play, Share, Heart } from "lucide-react"
import Image from "next/image"

interface MovieDetailsProps {
  movieId: string
}

export function MovieDetails({ movieId }: MovieDetailsProps) {
  // Mock movie data - in real app, fetch based on movieId
  const movie = {
    id: movieId,
    title: "Spider-Man: No Way Home",
    tagline: "The Multiverse Unleashed",
    description:
      "With Spider-Man's identity now revealed, <PERSON> asks Doctor Strange for help. When a spell goes wrong, dangerous foes from other worlds start to appear, forcing <PERSON> to discover what it truly means to be Spider-Man.",
    poster: "/placeholder.svg?height=600&width=400",
    backdrop: "/placeholder.svg?height=800&width=1200",
    rating: 8.4,
    duration: "2h 28min",
    releaseDate: "December 17, 2021",
    genre: ["Action", "Adventure", "Sci-Fi"],
    language: "English",
    director: "<PERSON>",
    writers: ["<PERSON>", "<PERSON>"],
    cast: ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"],
    certification: "PG-13",
    budget: "$200M",
    boxOffice: "$1.921B",
  }

  return (
    <div className="relative">
      {/* Backdrop */}
      <div className="relative h-[80vh] overflow-hidden">
        <Image src={movie.backdrop || "/placeholder.svg"} alt={movie.title} fill className="object-cover" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/60 to-black/30" />

        {/* Content */}
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-3 gap-8 items-center">
              {/* Poster */}
              <div className="lg:col-span-1">
                <Image
                  src={movie.poster || "/placeholder.svg"}
                  alt={movie.title}
                  width={400}
                  height={600}
                  className="rounded-lg shadow-2xl mx-auto"
                />
              </div>

              {/* Movie Info */}
              <div className="lg:col-span-2 text-white space-y-6">
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    {movie.genre.map((g) => (
                      <Badge key={g} variant="secondary" className="bg-red-600 text-white">
                        {g}
                      </Badge>
                    ))}
                  </div>
                  <h1 className="text-5xl md:text-6xl font-bold leading-tight">{movie.title}</h1>
                  <p className="text-xl text-red-400 font-medium">{movie.tagline}</p>
                </div>

                <div className="flex flex-wrap items-center gap-6 text-lg">
                  <div className="flex items-center gap-2">
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    <span className="font-bold">{movie.rating}</span>
                    <span className="text-gray-300">/10</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    <span>{movie.duration}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    <span>{movie.releaseDate}</span>
                  </div>
                  <Badge className="bg-yellow-600 text-white">{movie.certification}</Badge>
                </div>

                <p className="text-lg text-gray-200 leading-relaxed max-w-3xl">{movie.description}</p>

                <div className="grid md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Director:</span>
                    <span className="ml-2 font-medium">{movie.director}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Language:</span>
                    <span className="ml-2 font-medium">{movie.language}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Budget:</span>
                    <span className="ml-2 font-medium">{movie.budget}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Box Office:</span>
                    <span className="ml-2 font-medium">{movie.boxOffice}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-4">
                  <Button size="lg" className="bg-red-600 hover:bg-red-700">
                    Book Tickets
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-black"
                  >
                    <Play className="w-5 h-5 mr-2" />
                    Watch Trailer
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-black"
                  >
                    <Heart className="w-5 h-5 mr-2" />
                    Wishlist
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-white hover:text-black"
                  >
                    <Share className="w-5 h-5 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
