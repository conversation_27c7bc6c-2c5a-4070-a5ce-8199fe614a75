import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function SeatLegend() {
  const legendItems = [
    { color: "bg-green-200 border-green-400", label: "Available (Regular)", price: "₹150" },
    { color: "bg-purple-200 border-purple-400", label: "Available (Premium)", price: "₹200" },
    { color: "bg-yellow-200 border-yellow-400", label: "Available (VIP)", price: "₹300" },
    { color: "bg-red-600", label: "Selected", price: "" },
    { color: "bg-gray-400", label: "Occupied", price: "" },
    { color: "bg-blue-500", label: "Wheelchair Accessible", price: "₹150" },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Seat Legend</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {legendItems.map((item, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className={`w-6 h-6 rounded border-2 ${item.color}`} />
              <div className="flex-1">
                <p className="text-sm font-medium">{item.label}</p>
                {item.price && <p className="text-xs text-muted-foreground">{item.price}</p>}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
