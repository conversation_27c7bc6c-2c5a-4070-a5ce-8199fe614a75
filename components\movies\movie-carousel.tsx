import { MovieCard } from "./movie-card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface MovieCarouselProps {
  title: string
  category: string
}

export function MovieCarousel({ title, category }: MovieCarouselProps) {
  const movies = [
    {
      id: 1,
      title: "Spider-Man: No Way Home",
      genre: "Action, Adventure",
      rating: 8.4,
      duration: "2h 28min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 2,
      title: "Dune",
      genre: "Sci-Fi, Adventure",
      rating: 8.0,
      duration: "2h 35min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 3,
      title: "No Time to Die",
      genre: "Action, Thriller",
      rating: 7.3,
      duration: "2h 43min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 4,
      title: "The Batman",
      genre: "Action, Crime",
      rating: 7.8,
      duration: "2h 56min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
    {
      id: 5,
      title: "Top Gun: Maverick",
      genre: "Action, Drama",
      rating: 8.3,
      duration: "2h 10min",
      language: "English",
      poster: "/placeholder.svg?height=400&width=300",
    },
  ]

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold">{title}</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="icon">
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="icon">
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
        {movies.map((movie) => (
          <MovieCard key={movie.id} movie={movie} />
        ))}
      </div>
    </section>
  )
}
