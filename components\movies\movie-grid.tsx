import { MovieCard } from "./movie-card"
import { Button } from "@/components/ui/button"

export function MovieGrid() {
  const movies = Array.from({ length: 12 }, (_, i) => ({
    id: i + 1,
    title: `Movie Title ${i + 1}`,
    genre: "Action, Adventure",
    rating: 7.5 + i * 0.1,
    duration: "2h 15min",
    language: "English",
    poster: `/placeholder.svg?height=400&width=300`,
  }))

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground">Showing 12 of 156 movies</p>
        <select className="border rounded-md px-3 py-2 text-sm">
          <option>Sort by Popularity</option>
          <option>Sort by Rating</option>
          <option>Sort by Release Date</option>
          <option>Sort by Title</option>
        </select>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
        {movies.map((movie) => (
          <MovieCard key={movie.id} movie={movie} />
        ))}
      </div>

      <div className="flex justify-center">
        <Button variant="outline" size="lg">
          Load More Movies
        </Button>
      </div>
    </div>
  )
}
