import { TheaterCard } from "./theater-card"

export function TheaterList() {
  const theaters = [
    {
      id: 1,
      name: "PVR Cinemas",
      location: "Phoenix Mall, Whitefield",
      address: "Whitefield Main Road, Bangalore - 560066",
      distance: "2.5 km",
      rating: 4.2,
      totalReviews: 1250,
      image: "/placeholder.svg?height=200&width=300",
      amenities: ["Parking", "Food Court", "WiFi", "Wheelchair Access", "IMAX"],
      screens: 8,
      formats: ["2D", "3D", "IMAX", "4DX"],
      priceRange: "₹150 - ₹450",
      nextShow: "10:30 AM",
    },
    {
      id: 2,
      name: "INOX Multiplex",
      location: "Forum Mall, Koramangala",
      address: "Hosur Road, Koramangala, Bangalore - 560095",
      distance: "3.8 km",
      rating: 4.0,
      totalReviews: 980,
      image: "/placeholder.svg?height=200&width=300",
      amenities: ["Parking", "Food Court", "Wheelchair Access", "Premium Seating"],
      screens: 6,
      formats: ["2D", "3D", "Dolby Atmos"],
      priceRange: "₹180 - ₹380",
      nextShow: "11:00 AM",
    },
    {
      id: 3,
      name: "Cinepolis",
      location: "Orion Mall, Rajajinagar",
      address: "Dr. <PERSON>kumar Road, Rajajinagar, Bangalore - 560010",
      distance: "5.2 km",
      rating: 4.3,
      totalReviews: 1450,
      image: "/placeholder.svg?height=200&width=300",
      amenities: ["Parking", "Food Court", "WiFi", "Premium Seating", "4DX"],
      screens: 10,
      formats: ["2D", "3D", "4DX", "Dolby Atmos"],
      priceRange: "₹200 - ₹600",
      nextShow: "10:45 AM",
    },
    {
      id: 4,
      name: "Miraj Cinemas",
      location: "Garuda Mall, Magrath Road",
      address: "Magrath Road, Ashok Nagar, Bangalore - 560025",
      distance: "6.1 km",
      rating: 3.8,
      totalReviews: 750,
      image: "/placeholder.svg?height=200&width=300",
      amenities: ["Parking", "Food Court", "WiFi"],
      screens: 4,
      formats: ["2D", "3D"],
      priceRange: "₹120 - ₹300",
      nextShow: "11:30 AM",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground">Showing {theaters.length} theaters near you</p>
        <select className="border rounded-md px-3 py-2 text-sm">
          <option>Sort by Distance</option>
          <option>Sort by Rating</option>
          <option>Sort by Price</option>
          <option>Sort by Name</option>
        </select>
      </div>

      <div className="grid gap-6">
        {theaters.map((theater) => (
          <TheaterCard key={theater.id} theater={theater} />
        ))}
      </div>
    </div>
  )
}
