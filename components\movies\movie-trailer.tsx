import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Play, Volume2, Maximize } from "lucide-react"

export function MovieTrailer() {
  return (
    <section className="space-y-6">
      <h2 className="text-3xl font-bold">Watch Trailer</h2>

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="relative aspect-video bg-black">
            {/* Video placeholder */}
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-900 to-black">
              <div className="text-center space-y-4">
                <Button size="lg" className="bg-red-600 hover:bg-red-700 rounded-full w-20 h-20">
                  <Play className="w-8 h-8 ml-1" />
                </Button>
                <p className="text-white text-lg">Click to play trailer</p>
              </div>
            </div>

            {/* Video controls */}
            <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                  <Play className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                  <Volume2 className="w-4 h-4" />
                </Button>
                <span className="text-white text-sm">0:00 / 2:31</span>
              </div>

              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                <Maximize className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </section>
  )
}
