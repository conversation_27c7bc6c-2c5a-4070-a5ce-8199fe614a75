import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, Download, Share, Calendar, MapPin, Clock, Users, Mail, Phone } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export function BookingConfirmation() {
  const bookingData = {
    bookingId: "BK2024001234",
    qrCode: "/placeholder.svg?height=150&width=150",
    movie: {
      title: "Spider-Man: No Way Home",
      poster: "/placeholder.svg?height=120&width=80",
      rating: "PG-13",
      duration: "2h 28min",
      genre: "Action, Adventure, Sci-Fi",
    },
    theater: {
      name: "PVR Cinemas",
      location: "Phoenix Mall, Bangalore",
      screen: "Screen 3 - IMAX",
      address: "Whitefield Main Road, Bangalore - 560066",
    },
    showtime: {
      date: "Today, Dec 25, 2024",
      time: "7:30 PM",
      format: "IMAX",
    },
    seats: ["F7", "F8"],
    customer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+91 9876543210",
    },
    pricing: {
      tickets: 2,
      basePrice: 600,
      convenienceFee: 60,
      taxes: 66,
      total: 726,
    },
    bookingTime: "Dec 23, 2024 at 3:45 PM",
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Success Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <CheckCircle className="w-16 h-16 text-green-500" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-green-600">Booking Confirmed!</h1>
            <p className="text-muted-foreground mt-2">
              Your tickets have been booked successfully. You will receive a confirmation email shortly.
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Ticket Details */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Booking Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Movie Info */}
                <div className="flex gap-4">
                  <Image
                    src={bookingData.movie.poster || "/placeholder.svg"}
                    alt={bookingData.movie.title}
                    width={80}
                    height={120}
                    className="rounded-lg"
                  />
                  <div className="flex-1 space-y-2">
                    <h3 className="text-xl font-bold">{bookingData.movie.title}</h3>
                    <p className="text-muted-foreground">{bookingData.movie.genre}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <Badge>{bookingData.movie.rating}</Badge>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {bookingData.movie.duration}
                      </span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Theater & Showtime */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Theater Details
                    </h4>
                    <div className="space-y-1 text-sm">
                      <p className="font-medium">{bookingData.theater.name}</p>
                      <p className="text-muted-foreground">{bookingData.theater.location}</p>
                      <p className="text-muted-foreground">{bookingData.theater.screen}</p>
                      <p className="text-muted-foreground">{bookingData.theater.address}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      Show Details
                    </h4>
                    <div className="space-y-1 text-sm">
                      <p className="font-medium">{bookingData.showtime.date}</p>
                      <p className="font-medium">{bookingData.showtime.time}</p>
                      <Badge variant="outline">{bookingData.showtime.format}</Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Seats & Customer */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      Seat Details
                    </h4>
                    <div className="flex gap-2">
                      {bookingData.seats.map((seat) => (
                        <Badge key={seat} className="bg-red-100 text-red-700">
                          {seat}
                        </Badge>
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground">{bookingData.seats.length} seat(s) booked</p>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold">Customer Information</h4>
                    <div className="space-y-1 text-sm">
                      <p className="font-medium">{bookingData.customer.name}</p>
                      <p className="flex items-center gap-2 text-muted-foreground">
                        <Mail className="w-3 h-3" />
                        {bookingData.customer.email}
                      </p>
                      <p className="flex items-center gap-2 text-muted-foreground">
                        <Phone className="w-3 h-3" />
                        {bookingData.customer.phone}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>
                      Tickets ({bookingData.pricing.tickets}x ₹{bookingData.pricing.basePrice / 2})
                    </span>
                    <span>₹{bookingData.pricing.basePrice}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Convenience Fee</span>
                    <span>₹{bookingData.pricing.convenienceFee}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Taxes & Fees</span>
                    <span>₹{bookingData.pricing.taxes}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total Paid</span>
                    <span className="text-green-600">₹{bookingData.pricing.total}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">Booked on {bookingData.bookingTime}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* QR Code & Actions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Your Ticket</CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <div className="flex justify-center">
                  <Image
                    src={bookingData.qrCode || "/placeholder.svg"}
                    alt="QR Code"
                    width={150}
                    height={150}
                    className="border rounded-lg"
                  />
                </div>
                <div>
                  <p className="font-semibold">Booking ID</p>
                  <p className="text-lg font-mono text-red-600">{bookingData.bookingId}</p>
                </div>
                <p className="text-xs text-muted-foreground">
                  Show this QR code at the theater entrance for quick entry
                </p>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button className="w-full bg-red-600 hover:bg-red-700">
                <Download className="w-4 h-4 mr-2" />
                Download Ticket
              </Button>
              <Button variant="outline" className="w-full">
                <Share className="w-4 h-4 mr-2" />
                Share Ticket
              </Button>
              <Button variant="outline" className="w-full">
                <Mail className="w-4 h-4 mr-2" />
                Email Ticket
              </Button>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What's Next?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/movies">
                  <Button variant="ghost" className="w-full justify-start">
                    Book Another Show
                  </Button>
                </Link>
                <Link href="/dashboard">
                  <Button variant="ghost" className="w-full justify-start">
                    View All Bookings
                  </Button>
                </Link>
                <Link href="/dashboard/profile">
                  <Button variant="ghost" className="w-full justify-start">
                    Update Profile
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Important Information */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="font-semibold mb-3 text-blue-800">Important Information</h3>
            <ul className="space-y-2 text-sm text-blue-700">
              <li>• Please arrive at the theater at least 15 minutes before showtime</li>
              <li>• Carry a valid ID proof for verification</li>
              <li>• Outside food and beverages are not allowed</li>
              <li>• Tickets once booked cannot be cancelled or refunded</li>
              <li>• For any assistance, contact customer support at +91 1800-123-4567</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
