"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Clock, Star, TrendingUp, X } from "lucide-react"
import Image from "next/image"

export function EnhancedMovieSearch() {
  const [searchQuery, setSearchQuery] = useState("")
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [trendingSearches] = useState(["Spider-Man", "Avatar", "Batman", "Marvel", "Action movies"])
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Mock search suggestions
  const mockSuggestions = [
    {
      id: 1,
      title: "Spider-Man: No Way Home",
      type: "movie",
      poster: "/placeholder.svg?height=60&width=40",
      rating: 8.4,
      year: 2021,
    },
    {
      id: 2,
      title: "Spider-Man: Into the Spider-Verse",
      type: "movie",
      poster: "/placeholder.svg?height=60&width=40",
      rating: 8.4,
      year: 2018,
    },
    {
      id: 3,
      title: "Tom Holland",
      type: "actor",
      image: "/placeholder.svg?height=60&width=60",
    },
    {
      id: 4,
      title: "Action",
      type: "genre",
    },
  ]

  useEffect(() => {
    if (searchQuery.length > 2) {
      // Simulate API call delay
      const timer = setTimeout(() => {
        setSuggestions(mockSuggestions.filter((item) => item.title.toLowerCase().includes(searchQuery.toLowerCase())))
        setShowSuggestions(true)
      }, 300)

      return () => clearTimeout(timer)
    } else {
      setSuggestions([])
      setShowSuggestions(false)
    }
  }, [searchQuery])

  const handleSearch = (query: string) => {
    if (query.trim()) {
      // Add to recent searches
      setRecentSearches((prev) => {
        const updated = [query, ...prev.filter((item) => item !== query)].slice(0, 5)
        return updated
      })
      setShowSuggestions(false)
      // Perform actual search
      console.log("Searching for:", query)
    }
  }

  const clearRecentSearch = (searchToRemove: string) => {
    setRecentSearches((prev) => prev.filter((item) => item !== searchToRemove))
  }

  return (
    <div className="relative max-w-2xl mx-auto">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search movies, actors, directors, genres..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          className="pl-10 pr-4 h-12 text-lg"
        />
      </div>

      {/* Search Suggestions Dropdown */}
      {showSuggestions && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-50 max-h-96 overflow-y-auto">
          <CardContent className="p-0">
            {/* Search Suggestions */}
            {suggestions.length > 0 && (
              <div className="p-4 border-b">
                <h3 className="font-semibold mb-3 text-sm text-muted-foreground">SUGGESTIONS</h3>
                <div className="space-y-2">
                  {suggestions.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
                      onClick={() => handleSearch(item.title)}
                    >
                      {item.type === "movie" && (
                        <>
                          <Image
                            src={item.poster || "/placeholder.svg"}
                            alt={item.title}
                            width={40}
                            height={60}
                            className="rounded"
                          />
                          <div className="flex-1">
                            <p className="font-medium">{item.title}</p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>{item.year}</span>
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                <span>{item.rating}</span>
                              </div>
                            </div>
                          </div>
                          <Badge variant="outline">Movie</Badge>
                        </>
                      )}

                      {item.type === "actor" && (
                        <>
                          <Image
                            src={item.image || "/placeholder.svg"}
                            alt={item.title}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                          <div className="flex-1">
                            <p className="font-medium">{item.title}</p>
                          </div>
                          <Badge variant="outline">Actor</Badge>
                        </>
                      )}

                      {item.type === "genre" && (
                        <>
                          <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                            <Search className="w-4 h-4 text-red-600" />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">{item.title}</p>
                          </div>
                          <Badge variant="outline">Genre</Badge>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <div className="p-4 border-b">
                <h3 className="font-semibold mb-3 text-sm text-muted-foreground flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  RECENT SEARCHES
                </h3>
                <div className="space-y-1">
                  {recentSearches.map((search, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg group"
                    >
                      <span className="cursor-pointer flex-1" onClick={() => handleSearch(search)}>
                        {search}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => clearRecentSearch(search)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Trending Searches */}
            <div className="p-4">
              <h3 className="font-semibold mb-3 text-sm text-muted-foreground flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                TRENDING SEARCHES
              </h3>
              <div className="flex flex-wrap gap-2">
                {trendingSearches.map((search, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleSearch(search)}
                    className="text-xs"
                  >
                    {search}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
