"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

type SeatStatus = "available" | "occupied" | "selected" | "wheelchair"

interface Seat {
  id: string
  row: string
  number: number
  status: SeatStatus
  price: number
  type: "regular" | "premium" | "vip"
}

export function SeatMap() {
  const [selectedSeats, setSelectedSeats] = useState<string[]>([])

  // Generate seat data
  const generateSeats = (): Seat[] => {
    const seats: Seat[] = []
    const rows = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]

    rows.forEach((row, rowIndex) => {
      for (let seatNum = 1; seatNum <= 12; seatNum++) {
        const seatId = `${row}${seatNum}`
        let type: "regular" | "premium" | "vip" = "regular"
        let price = 150

        // VIP seats (first 3 rows)
        if (rowIndex < 3) {
          type = "vip"
          price = 300
        }
        // Premium seats (middle rows)
        else if (rowIndex < 6) {
          type = "premium"
          price = 200
        }

        // Random occupied seats
        const isOccupied = Math.random() < 0.3

        seats.push({
          id: seatId,
          row,
          number: seatNum,
          status: isOccupied ? "occupied" : "available",
          price,
          type,
        })
      }
    })

    return seats
  }

  const [seats] = useState<Seat[]>(generateSeats())

  const handleSeatClick = (seatId: string, seat: Seat) => {
    if (seat.status === "occupied") return

    setSelectedSeats((prev) => (prev.includes(seatId) ? prev.filter((id) => id !== seatId) : [...prev, seatId]))
  }

  const getSeatColor = (seat: Seat) => {
    if (selectedSeats.includes(seat.id)) {
      return "bg-red-600 hover:bg-red-700 text-white"
    }

    switch (seat.status) {
      case "occupied":
        return "bg-gray-400 cursor-not-allowed"
      case "wheelchair":
        return "bg-blue-500 hover:bg-blue-600 text-white"
      default:
        switch (seat.type) {
          case "vip":
            return "bg-yellow-200 hover:bg-yellow-300 border-yellow-400"
          case "premium":
            return "bg-purple-200 hover:bg-purple-300 border-purple-400"
          default:
            return "bg-green-200 hover:bg-green-300 border-green-400"
        }
    }
  }

  const groupedSeats = seats.reduce(
    (acc, seat) => {
      if (!acc[seat.row]) acc[seat.row] = []
      acc[seat.row].push(seat)
      return acc
    },
    {} as Record<string, Seat[]>,
  )

  return (
    <div className="space-y-4">
      {/* Seat Grid */}
      <div className="space-y-2">
        {Object.entries(groupedSeats).map(([row, rowSeats]) => (
          <div key={row} className="flex items-center justify-center gap-1">
            <span className="w-6 text-center font-medium text-sm">{row}</span>
            <div className="flex gap-1">
              {rowSeats.slice(0, 6).map((seat) => (
                <Button
                  key={seat.id}
                  variant="outline"
                  size="sm"
                  className={cn("w-8 h-8 p-0 text-xs border-2 transition-all", getSeatColor(seat))}
                  onClick={() => handleSeatClick(seat.id, seat)}
                  disabled={seat.status === "occupied"}
                >
                  {seat.number}
                </Button>
              ))}
            </div>
            <div className="w-8" /> {/* Aisle */}
            <div className="flex gap-1">
              {rowSeats.slice(6, 12).map((seat) => (
                <Button
                  key={seat.id}
                  variant="outline"
                  size="sm"
                  className={cn("w-8 h-8 p-0 text-xs border-2 transition-all", getSeatColor(seat))}
                  onClick={() => handleSeatClick(seat.id, seat)}
                  disabled={seat.status === "occupied"}
                >
                  {seat.number}
                </Button>
              ))}
            </div>
            <span className="w-6 text-center font-medium text-sm">{row}</span>
          </div>
        ))}
      </div>

      {/* Selected Seats Info */}
      {selectedSeats.length > 0 && (
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <p className="font-medium">Selected Seats: {selectedSeats.join(", ")}</p>
          <p className="text-sm text-muted-foreground">{selectedSeats.length} seat(s) selected</p>
        </div>
      )}
    </div>
  )
}
