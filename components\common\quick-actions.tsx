import { Card } from "@/components/ui/card"
import { Film, MapPin, Clock, Ticket } from "lucide-react"
import Link from "next/link"

export function QuickActions() {
  const actions = [
    {
      icon: Film,
      title: "Browse Movies",
      description: "Discover what's playing",
      href: "/movies",
    },
    {
      icon: MapPin,
      title: "Find Theaters",
      description: "Locate nearby cinemas",
      href: "/theaters",
    },
    {
      icon: Clock,
      title: "Showtimes",
      description: "Check movie schedules",
      href: "/showtimes",
    },
    {
      icon: Ticket,
      title: "My Bookings",
      description: "View your tickets",
      href: "/dashboard",
    },
  ]

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {actions.map((action, index) => (
        <Link key={index} href={action.href}>
          <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer group">
            <div className="text-center space-y-3">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-200 transition-colors">
                <action.icon className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="font-semibold">{action.title}</h3>
                <p className="text-sm text-muted-foreground">{action.description}</p>
              </div>
            </div>
          </Card>
        </Link>
      ))}
    </div>
  )
}
