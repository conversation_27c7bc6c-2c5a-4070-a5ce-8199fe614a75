// API Response wrapper
export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
  timestamp: string
  path?: string
  status?: number
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string
  preferredGenre?: string
  preferredTheaterId?: number
}

export interface AuthResponse {
  token: string
  refreshToken: string
  type: string
  id: number
  email: string
  firstName: string
  lastName: string
  role: string
  preferredGenre?: string | null
  preferredTheaterId?: number | null
  fullName: string
  admin: boolean
}

export interface User {
  id: number
  email: string
  firstName: string
  lastName: string
  phone: string
  role: 'USER' | 'ADMIN'
  preferredGenre?: string
  preferredTheaterId?: number
  isActive: boolean
  createdAt: string
}

// Movie Types
export interface Movie {
  id: number
  title: string
  description: string
  genre: string
  durationMinutes: number
  durationFormatted: string
  rating: string
  releaseDate: string
  posterUrl: string
  trailerUrl?: string
  language: string
  director: string
  cast: string
  isActive: boolean
  tmdbId?: number
  createdAt: string
  currentlyPlaying: boolean
}

export interface PaginatedResponse<T> {
  content: T[]
  pageable: {
    pageNumber: number
    pageSize: number
    sort: {
      sorted: boolean
      unsorted: boolean
      empty: boolean
    }
    offset: number
    paged: boolean
    unpaged: boolean
  }
  totalElements: number
  totalPages: number
  last: boolean
  first: boolean
  numberOfElements: number
  size: number
  number: number
  sort: {
    sorted: boolean
    unsorted: boolean
    empty: boolean
  }
  empty: boolean
}

// Theater Types
export interface Theater {
  id: number
  name: string
  location: string
  capacity: number
  screenType: 'STANDARD' | 'IMAX' | '3D' | '4DX'
  hasWheelchairAccess: boolean
  description?: string
  isActive: boolean
  createdAt: string
}

// Seat Types
export interface Seat {
  id: number
  seatNumber: string
  rowNumber: string
  seatType: 'REGULAR' | 'PREMIUM' | 'VIP'
  basePrice: number
  isWheelchairAccessible: boolean
  isActive: boolean
  theater: Theater
}

// Showtime Types
export interface Showtime {
  id: number
  movie: Movie
  theater: Theater
  showDate: string
  showTime: string
  basePrice: number
  availableSeats: number
  totalSeats: number
  isActive: boolean
  displayDate: string
  displayTime: string
}

// Booking Types
export interface CreateBookingRequest {
  showtimeId: number
  seatIds: number[]
  customerName: string
  customerEmail: string
  customerPhone: string
}

export interface Booking {
  id: number
  bookingReference: string
  totalAmount: number
  bookingStatus: 'Confirmed' | 'Cancelled' | 'Pending'
  customerName: string
  customerEmail: string
  customerPhone: string
  bookingDate: string
  showtime: {
    id: number
    movieTitle: string
    theaterName: string
    displayDate: string
    displayTime: string
    basePrice: number
  }
  seats: {
    id: number
    seatLabel: string
    seatType: 'REGULAR' | 'PREMIUM' | 'VIP'
    price: number
  }[]
  canBeCancelled: boolean
}

// Seat Management Types
export interface SeatHoldRequest {
  showtimeId: number
  seatIds: number[]
  action: 'HOLD' | 'RELEASE'
}

// Query Parameters
export interface PaginationParams {
  page?: number
  size?: number
  sortBy?: string
  sortDir?: 'asc' | 'desc'
}

export interface MovieQueryParams extends PaginationParams {
  query?: string
}

export interface ShowtimeQueryParams extends PaginationParams {
  date?: string
  movieId?: number
  theaterId?: number
}

export interface TheaterQueryParams {
  date?: string
  minSeats?: number
  maxSeats?: number
}

// User Management Types
export interface UpdateProfileRequest {
  firstName?: string
  lastName?: string
  phone?: string
}

export interface UpdatePreferencesRequest {
  preferredGenre?: string
  preferredTheaterId?: number
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

// Revenue Types (Admin)
export interface RevenueQueryParams {
  startDate: string
  endDate: string
}
