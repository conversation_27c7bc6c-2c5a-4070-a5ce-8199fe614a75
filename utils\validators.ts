export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePhone = (phone: string): boolean => {
  // Indian phone number validation (10 digits)
  const phoneRegex = /^[6-9]\d{9}$/
  const cleaned = phone.replace(/\D/g, "")
  return phoneRegex.test(cleaned)
}

export const validatePassword = (
  password: string,
): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/\d/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push("Password must contain at least one special character")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

export const validateName = (name: string): boolean => {
  return name.trim().length >= 2 && /^[a-zA-Z\s]+$/.test(name.trim())
}

export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0
}

export const validateSeatSelection = (
  seats: string[],
): {
  isValid: boolean
  error?: string
} => {
  if (seats.length === 0) {
    return { isValid: false, error: "Please select at least one seat" }
  }

  if (seats.length > 6) {
    return { isValid: false, error: "Maximum 6 seats can be selected" }
  }

  return { isValid: true }
}

export const validateBookingForm = (data: {
  customerName: string
  customerEmail: string
  customerPhone: string
  seats: string[]
}): {
  isValid: boolean
  errors: Record<string, string>
} => {
  const errors: Record<string, string> = {}

  if (!validateRequired(data.customerName)) {
    errors.customerName = "Name is required"
  } else if (!validateName(data.customerName)) {
    errors.customerName = "Please enter a valid name"
  }

  if (!validateRequired(data.customerEmail)) {
    errors.customerEmail = "Email is required"
  } else if (!validateEmail(data.customerEmail)) {
    errors.customerEmail = "Please enter a valid email address"
  }

  if (!validateRequired(data.customerPhone)) {
    errors.customerPhone = "Phone number is required"
  } else if (!validatePhone(data.customerPhone)) {
    errors.customerPhone = "Please enter a valid 10-digit phone number"
  }

  const seatValidation = validateSeatSelection(data.seats)
  if (!seatValidation.isValid) {
    errors.seats = seatValidation.error!
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}
