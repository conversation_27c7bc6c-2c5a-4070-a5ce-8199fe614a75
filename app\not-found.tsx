import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Film, Home, Search } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        <div className="space-y-4">
          <Film className="w-24 h-24 mx-auto text-red-600" />
          <h1 className="text-6xl font-bold text-gray-900">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700">Page Not Found</h2>
          <p className="text-gray-600">
            Sorry, we couldn't find the page you're looking for. The movie might have ended or the page might have been
            moved.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button asChild className="bg-red-600 hover:bg-red-700">
            <Link href="/" className="flex items-center gap-2">
              <Home className="w-4 h-4" />
              Go Home
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/movies" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              Browse Movies
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
