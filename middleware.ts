import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Since we're using localStorage for tokens, we can't check auth status in middleware
  // We'll handle auth checks on the client side instead
  // This middleware will only handle basic route protection

  // Define public routes that don't need auth
  const publicRoutes = ["/", "/movies", "/showtimes", "/auth/login", "/auth/register"]
  const isPublicRoute = publicRoutes.some((route) => pathname === route || pathname.startsWith(route))

  // Allow access to public routes and API routes
  if (isPublicRoute || pathname.startsWith("/api") || pathname.startsWith("/_next")) {
    return NextResponse.next()
  }

  // For protected routes, let the client-side auth check handle redirects
  return NextResponse.next()
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
}
