"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CreditCard, Smartphone, Wallet, Shield, Clock } from "lucide-react"
import Image from "next/image"
import { useRouter } from "next/navigation"

export function PaymentForm() {
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState("card")
  const router = useRouter()

  const bookingData = {
    movie: {
      title: "Spider-Man: No Way Home",
      poster: "/placeholder.svg?height=120&width=80",
    },
    theater: "PVR Cinemas - Phoenix Mall",
    date: "Today, Dec 25",
    time: "7:30 PM",
    seats: ["F7", "F8"],
    pricing: {
      tickets: 2,
      basePrice: 600,
      convenienceFee: 60,
      taxes: 66,
      total: 726,
    },
  }

  const handlePayment = async () => {
    setIsProcessing(true)

    // Simulate payment processing
    await new Promise((resolve) => setTimeout(resolve, 3000))

    setIsProcessing(false)
    router.push("/booking/confirmation")
  }

  if (isProcessing) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <Card>
          <CardContent className="p-8 text-center space-y-6">
            <div className="animate-spin w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full mx-auto"></div>
            <div>
              <h2 className="text-2xl font-bold mb-2">Processing Payment</h2>
              <p className="text-muted-foreground">Please wait while we process your payment securely...</p>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Shield className="w-4 h-4" />
              <span>Your payment is secured with 256-bit SSL encryption</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Payment Form */}
        <div className="lg:col-span-2 space-y-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">Complete Your Payment</h1>
            <p className="text-muted-foreground">Choose your preferred payment method and complete the booking</p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Payment Method</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={paymentMethod} onValueChange={setPaymentMethod}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="card" className="flex items-center gap-2">
                    <CreditCard className="w-4 h-4" />
                    Card
                  </TabsTrigger>
                  <TabsTrigger value="upi" className="flex items-center gap-2">
                    <Smartphone className="w-4 h-4" />
                    UPI
                  </TabsTrigger>
                  <TabsTrigger value="wallet" className="flex items-center gap-2">
                    <Wallet className="w-4 h-4" />
                    Wallet
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="card" className="space-y-6 mt-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="cardNumber">Card Number</Label>
                      <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cardName">Cardholder Name</Label>
                      <Input id="cardName" placeholder="John Doe" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiry">Expiry Date</Label>
                      <Input id="expiry" placeholder="MM/YY" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cvv">CVV</Label>
                      <Input id="cvv" placeholder="123" />
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Shield className="w-4 h-4" />
                    <span>Your card details are encrypted and secure</span>
                  </div>
                </TabsContent>

                <TabsContent value="upi" className="space-y-6 mt-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="upiId">UPI ID</Label>
                      <Input id="upiId" placeholder="yourname@paytm" />
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <Button variant="outline" className="flex items-center gap-2">
                        <Image src="/placeholder.svg?height=20&width=20" alt="GPay" width={20} height={20} />
                        Google Pay
                      </Button>
                      <Button variant="outline" className="flex items-center gap-2">
                        <Image src="/placeholder.svg?height=20&width=20" alt="PhonePe" width={20} height={20} />
                        PhonePe
                      </Button>
                      <Button variant="outline" className="flex items-center gap-2">
                        <Image src="/placeholder.svg?height=20&width=20" alt="Paytm" width={20} height={20} />
                        Paytm
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="wallet" className="space-y-6 mt-6">
                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline" className="h-16 flex flex-col items-center gap-2">
                      <Image src="/placeholder.svg?height=24&width=24" alt="Paytm Wallet" width={24} height={24} />
                      <span>Paytm Wallet</span>
                      <span className="text-xs text-muted-foreground">Balance: ₹1,250</span>
                    </Button>
                    <Button variant="outline" className="h-16 flex flex-col items-center gap-2">
                      <Image src="/placeholder.svg?height=24&width=24" alt="Amazon Pay" width={24} height={24} />
                      <span>Amazon Pay</span>
                      <span className="text-xs text-muted-foreground">Balance: ₹850</span>
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Promo Code */}
          <Card>
            <CardHeader>
              <CardTitle>Promo Code</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3">
                <Input placeholder="Enter promo code" />
                <Button variant="outline">Apply</Button>
              </div>
              <div className="mt-3 space-y-2">
                <p className="text-sm font-medium">Available Offers:</p>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">FIRST50</p>
                      <p className="text-sm text-muted-foreground">Get 50% off on your first booking</p>
                    </div>
                    <Button size="sm" variant="outline">
                      Apply
                    </Button>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">WEEKEND20</p>
                      <p className="text-sm text-muted-foreground">20% off on weekend bookings</p>
                    </div>
                    <Button size="sm" variant="outline">
                      Apply
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Booking Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Booking Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-3">
                <Image
                  src={bookingData.movie.poster || "/placeholder.svg"}
                  alt={bookingData.movie.title}
                  width={60}
                  height={90}
                  className="rounded-lg"
                />
                <div className="flex-1">
                  <h3 className="font-semibold">{bookingData.movie.title}</h3>
                  <p className="text-sm text-muted-foreground">{bookingData.theater}</p>
                  <p className="text-sm text-muted-foreground">
                    {bookingData.date} at {bookingData.time}
                  </p>
                  <div className="flex gap-1 mt-2">
                    {bookingData.seats.map((seat) => (
                      <Badge key={seat} variant="outline">
                        {seat}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>
                    Tickets ({bookingData.pricing.tickets}x ₹{bookingData.pricing.basePrice / 2})
                  </span>
                  <span>₹{bookingData.pricing.basePrice}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Convenience Fee</span>
                  <span>₹{bookingData.pricing.convenienceFee}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Taxes</span>
                  <span>₹{bookingData.pricing.taxes}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total Amount</span>
                  <span>₹{bookingData.pricing.total}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timer */}
          <Card className="bg-orange-50 border-orange-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-orange-700">
                <Clock className="w-4 h-4" />
                <span className="font-medium">Complete payment in 04:32</span>
              </div>
              <p className="text-sm text-orange-600 mt-1">Your seats will be released if payment is not completed</p>
            </CardContent>
          </Card>

          {/* Payment Button */}
          <Button className="w-full bg-red-600 hover:bg-red-700 h-12 text-lg" onClick={handlePayment}>
            Pay ₹{bookingData.pricing.total}
          </Button>

          {/* Security Info */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Shield className="w-4 h-4" />
              <span>100% Safe & Secure Payments</span>
            </div>
            <div className="flex justify-center gap-4">
              <Image src="/placeholder.svg?height=20&width=40" alt="Visa" width={40} height={20} />
              <Image src="/placeholder.svg?height=20&width=40" alt="Mastercard" width={40} height={20} />
              <Image src="/placeholder.svg?height=20&width=40" alt="RuPay" width={40} height={20} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
