# Cinema Booking System - Frontend Development Prompt

## Project Overview
You are tasked with creating a modern, responsive frontend for a comprehensive Cinema Booking System. The backend is a Spring Boot REST API with JWT authentication, comprehensive movie management, theater seat booking, and user management features.

## Backend API Configuration
- **Base URL**: `http://localhost:8080/api`
- **Authentication**: JWT Bearer tokens
- **CORS**: Enabled for `http://localhost:3000` and `http://localhost:3001`
- **Content-Type**: `application/json`

## Authentication System

### JWT Token Structure
- **Access Token**: 24 hours expiration
- **Refresh Token**: 7 days expiration
- **Header Format**: `Authorization: Bearer <token>`

### Auth Endpoints

#### POST /auth/login
**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "refresh_token_here",
    "type": "Bearer",
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER",
    "preferredGenre": "Action",
    "preferredTheaterId": 1
  },
  "timestamp": "2024-01-15T10:30:00"
}
```

#### POST /auth/register
**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Jane",
  "lastName": "Smith",
  "phone": "+1234567890",
  "preferredGenre": "Comedy",
  "preferredTheaterId": 2
}
```
**Response:** Same as login response

#### POST /auth/refresh-token
**Request Body:** `"refresh_token_string"`
**Response:** New token pair

#### GET /auth/validate-token
**Headers:** `Authorization: Bearer <token>`
**Response:**
```json
{
  "success": true,
  "message": "Token validation result",
  "data": true
}
```

#### GET /auth/user-info
**Headers:** `Authorization: Bearer <token>`
**Response:**
```json
{
  "success": true,
  "message": "User info retrieved",
  "data": "<EMAIL>"
}
```

## Movie Management

#### GET /movies
**Query Parameters:**
- `page` (default: 0)
- `size` (default: 20, max: 100)
- `sortBy` (default: "title")
- `sortDir` (default: "asc")

**Response:**
```json
{
  "success": true,
  "message": "Movies retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "title": "The Matrix",
        "description": "A computer hacker learns...",
        "genre": "Sci-Fi",
        "durationMinutes": 136,
        "durationFormatted": "2h 16m",
        "rating": "R",
        "releaseDate": "1999-03-31",
        "posterUrl": "https://image.tmdb.org/t/p/w500/poster.jpg",
        "trailerUrl": "https://youtube.com/watch?v=...",
        "language": "English",
        "director": "The Wachowskis",
        "cast": "Keanu Reeves, Laurence Fishburne",
        "isActive": true,
        "tmdbId": 603,
        "createdAt": "2024-01-15T10:00:00",
        "currentlyPlaying": true
      }
    ],
    "pageable": {...},
    "totalElements": 50,
    "totalPages": 3,
    "last": false,
    "first": true
  }
}
```

#### GET /movies/{id}
**Response:** Single movie object

#### GET /movies/search
**Query Parameters:**
- `query` (required)
- `page`, `size`, `sortBy`, `sortDir`

#### GET /movies/genre/{genre}
**Query Parameters:** `page`, `size`, `sortBy`, `sortDir`

#### GET /movies/featured
**Response:** List of featured movies

#### GET /movies/now-playing
**Response:** List of currently playing movies

#### GET /movies/upcoming
**Response:** List of upcoming movies

#### GET /movies/with-showtimes
**Response:** List of movies that have showtimes

## Theater Management

#### GET /theaters
**Response:**
```json
{
  "success": true,
  "message": "Theaters retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Grand Cinema Hall 1",
      "location": "Downtown Mall, Level 2",
      "capacity": 150,
      "screenType": "STANDARD",
      "hasWheelchairAccess": true,
      "description": "Premium theater with comfortable seating",
      "isActive": true,
      "createdAt": "2024-01-15T10:00:00"
    }
  ]
}
```

#### GET /theaters/{id}
**Response:** Single theater object

#### GET /theaters/{id}/seats
**Response:** Theater object with seats array

#### GET /theaters/3d
**Response:** List of 3D theaters

#### GET /theaters/imax
**Response:** List of IMAX theaters

#### GET /theaters/capacity
**Query Parameters:**
- `minSeats` (optional)
- `maxSeats` (optional)

#### GET /theaters/with-showtimes
**Query Parameters:**
- `date` (optional, format: YYYY-MM-DD)

#### GET /theaters/movie/{movieId}
**Response:** List of theaters showing specific movie

## Showtime Management

#### GET /showtimes
**Query Parameters:**
- `date` (optional, format: YYYY-MM-DD)
- `movieId` (optional)
- `theaterId` (optional)

**Response:**
```json
{
  "success": true,
  "message": "Showtimes retrieved successfully",
  "data": [
    {
      "id": 1,
      "movie": {...},
      "theater": {...},
      "showDate": "2024-01-20",
      "showTime": "19:30:00",
      "basePrice": 12.50,
      "availableSeats": 120,
      "totalSeats": 150,
      "isActive": true,
      "displayDate": "January 20, 2024",
      "displayTime": "7:30 PM"
    }
  ]
}
```

#### GET /showtimes/{id}
**Response:** Single showtime object

#### GET /showtimes/{id}/availability
**Response:** Seat availability information

#### GET /showtimes/movie/{movieId}
**Query Parameters:** `page`, `size`, `sortBy`, `sortDir`

#### GET /showtimes/theater/{theaterId}
**Query Parameters:** `page`, `size`, `sortBy`, `sortDir`

#### GET /showtimes/upcoming
**Response:** List of upcoming showtimes

#### GET /showtimes/today
**Response:** List of today's showtimes

## Seat Management

#### GET /seats/theater/{theaterId}
**Response:**
```json
{
  "success": true,
  "message": "Theater seats retrieved successfully",
  "data": [
    {
      "id": 1,
      "seatNumber": "A1",
      "rowNumber": "A",
      "seatType": "REGULAR",
      "basePrice": 12.50,
      "isWheelchairAccessible": false,
      "isActive": true,
      "theater": {...}
    }
  ]
}
```

#### GET /seats/showtime/{showtimeId}
**Response:** Available seats for specific showtime

#### GET /seats/theater/{theaterId}/available
**Query Parameters:**
- `showtimeId` (required)

#### GET /seats/theater/{theaterId}/row/{rowNumber}
**Response:** Seats in specific row

#### POST /seats/hold (🔒 Auth Required)
**Request Body:**
```json
{
  "showtimeId": 1,
  "seatIds": [1, 2, 3],
  "action": "HOLD"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Seats held successfully",
  "data": true
}
```

#### POST /seats/release (🔒 Auth Required)
**Request Body:**
```json
{
  "showtimeId": 1,
  "seatIds": [1, 2, 3],
  "action": "RELEASE"
}
```

## Booking Management

#### POST /bookings (🔒 Auth Required)
**Request Body:**
```json
{
  "showtimeId": 1,
  "seatIds": [1, 2, 3],
  "customerName": "John Doe",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+1234567890"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Booking created successfully",
  "data": {
    "id": 1,
    "bookingReference": "BK123456",
    "totalAmount": 37.50,
    "bookingStatus": "Confirmed",
    "customerName": "John Doe",
    "customerEmail": "<EMAIL>",
    "customerPhone": "+1234567890",
    "bookingDate": "2024-01-15T10:30:00",
    "showtime": {
      "id": 1,
      "movieTitle": "The Matrix",
      "theaterName": "Grand Cinema Hall 1",
      "displayDate": "January 20, 2024",
      "displayTime": "7:30 PM",
      "basePrice": 12.50
    },
    "seats": [
      {
        "id": 1,
        "seatLabel": "A1",
        "seatType": "REGULAR",
        "price": 12.50
      }
    ],
    "canBeCancelled": true
  }
}
```

#### GET /bookings/{id} (🔒 Auth Required)
**Response:** Single booking object

#### GET /bookings/reference/{reference} (🔒 Auth Required)
**Response:** Booking by reference number

#### GET /bookings/user (🔒 Auth Required)
**Query Parameters:** `page`, `size`, `sortBy`, `sortDir`
**Response:** User's bookings (paginated)

#### PUT /bookings/{id}/cancel (🔒 Auth Required)
**Response:** Updated booking object

#### PUT /bookings/{id}/confirm (🔒 Admin Required)
**Response:** Confirmed booking object

#### GET /bookings/admin/all (🔒 Admin Required)
**Query Parameters:** `page`, `size`, `sortBy`, `sortDir`
**Response:** All bookings (paginated)

#### GET /bookings/admin/revenue (🔒 Admin Required)
**Query Parameters:**
- `startDate` (required, format: YYYY-MM-DD)
- `endDate` (required, format: YYYY-MM-DD)
**Response:**
```json
{
  "success": true,
  "message": "Revenue retrieved successfully",
  "data": 15750.00
}
```

## User Management

#### GET /users/profile (🔒 Auth Required)
**Response:**
```json
{
  "success": true,
  "message": "User profile retrieved successfully",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "role": "USER",
    "preferredGenre": "Action",
    "preferredTheaterId": 1,
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00"
  }
}
```

#### PUT /users/profile (🔒 Auth Required)
**Request Body:** User object with updated fields
**Response:** Updated user object

#### PUT /users/preferences (🔒 Auth Required)
**Request Body:**
```json
{
  "preferredGenre": "Comedy",
  "preferredTheaterId": 2
}
```

#### PUT /users/password (🔒 Auth Required)
**Request Body:**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123"
}
```

#### GET /users/booking-history (🔒 Auth Required)
**Query Parameters:** `page`, `size`, `sortBy`, `sortDir`

#### DELETE /users/account (🔒 Auth Required)
**Response:** Success confirmation

## Error Handling

All API responses follow this structure:
```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "timestamp": "2024-01-15T10:30:00",
  "path": "/api/endpoint",
  "status": 400
}
```

Common HTTP Status Codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Frontend Requirements

### Technology Stack Recommendations
- **Framework**: React 18+ with TypeScript
- **State Management**: Redux Toolkit or Zustand
- **Routing**: React Router v6
- **UI Library**: Material-UI, Ant Design, or Tailwind CSS
- **HTTP Client**: Axios with interceptors
- **Form Handling**: React Hook Form with Yup validation
- **Date Handling**: date-fns or dayjs

### Key Features to Implement

1. **Authentication Flow**
   - Login/Register forms with validation
   - JWT token management with auto-refresh
   - Protected routes and role-based access
   - Logout functionality

2. **Movie Browsing**
   - Movie catalog with search and filtering
   - Movie details with trailers and cast info
   - Genre-based browsing
   - Featured and upcoming movies sections

3. **Theater & Showtime Selection**
   - Theater listing with details
   - Showtime selection by movie/theater/date
   - Interactive seat map with real-time availability
   - Seat type indicators (Regular, Premium, VIP)

4. **Booking Process**
   - Multi-step booking wizard
   - Seat selection with hold/release functionality
   - Booking confirmation with reference number
   - Payment integration (future enhancement)

5. **User Dashboard**
   - Profile management
   - Booking history with cancellation options
   - Preferences settings
   - Admin panel for admin users

6. **Responsive Design**
   - Mobile-first approach
   - Touch-friendly seat selection
   - Optimized for tablets and desktops

### Implementation Notes

- Implement proper error handling for all API calls
- Use loading states and skeleton screens
- Implement optimistic updates where appropriate
- Cache frequently accessed data (movies, theaters)
- Implement real-time seat availability updates
- Add proper accessibility features
- Use environment variables for API configuration
- Implement proper form validation matching backend constraints
- Add confirmation dialogs for destructive actions
- Implement proper logout on token expiration

### Security Considerations

- Store JWT tokens securely (httpOnly cookies recommended)
- Implement CSRF protection if using cookies
- Validate all user inputs on frontend
- Implement proper error messages without exposing sensitive info
- Use HTTPS in production
- Implement rate limiting on sensitive operations

This comprehensive API documentation should provide everything needed to build a fully functional cinema booking frontend that integrates seamlessly with the Spring Boot backend.
