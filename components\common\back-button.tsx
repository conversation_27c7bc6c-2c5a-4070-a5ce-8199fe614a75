"use client"

import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

interface BackButtonProps {
  fallbackUrl?: string
  className?: string
}

export function BackButton({ fallbackUrl = "/", className }: BackButtonProps) {
  const router = useRouter()

  const handleBack = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      router.push(fallbackUrl)
    }
  }

  return (
    <Button variant="ghost" onClick={handleBack} className={className}>
      <ArrowLeft className="w-4 h-4 mr-2" />
      Back
    </Button>
  )
}
