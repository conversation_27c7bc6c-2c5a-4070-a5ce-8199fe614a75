import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Clock } from "lucide-react"
import Image from "next/image"

export function ShowtimesHeader() {
  const selectedMovie = {
    title: "Spider-Man: No Way Home",
    poster: "/placeholder.svg?height=120&width=80",
    rating: 8.4,
    duration: "2h 28min",
    genre: "Action, Adventure, Sci-Fi",
    certification: "PG-13",
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex gap-6">
          <Image
            src={selectedMovie.poster || "/placeholder.svg"}
            alt={selectedMovie.title}
            width={80}
            height={120}
            className="rounded-lg"
          />
          <div className="flex-1 space-y-3">
            <div>
              <h1 className="text-3xl font-bold">{selectedMovie.title}</h1>
              <p className="text-muted-foreground">{selectedMovie.genre}</p>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="font-semibold">{selectedMovie.rating}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span>{selectedMovie.duration}</span>
              </div>
              <Badge>{selectedMovie.certification}</Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
