"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Star, Calendar } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

export function FeaturedMovies() {
  const router = useRouter()

  const featuredMovies = [
    {
      id: 1,
      title: "Avatar: The Way of Water",
      description:
        "Set more than a decade after the events of the first film, Avatar: The Way of Water begins to tell the story of the <PERSON>ly family.",
      genre: "Sci-Fi, Adventure",
      rating: 7.6,
      releaseDate: "Dec 16, 2022",
      poster: "/placeholder.svg?height=300&width=200",
      backdrop: "/placeholder.svg?height=400&width=600",
    },
    {
      id: 2,
      title: "Black Panther: Wakanda Forever",
      description:
        "Queen Ramonda, Shuri, M'Baku, Okoye and the Dora Milaje fight to protect their nation from intervening world powers.",
      genre: "Action, Adventure",
      rating: 6.7,
      releaseDate: "Nov 11, 2022",
      poster: "/placeholder.svg?height=300&width=200",
      backdrop: "/placeholder.svg?height=400&width=600",
    },
  ]

  const handleBookTickets = (movieId: number) => {
    router.push(`/movies/${movieId}`)
  }

  const handleWatchTrailer = (movieId: number) => {
    // Implement trailer functionality
    console.log(`Playing trailer for movie ${movieId}`)
  }

  return (
    <section className="space-y-6">
      <h2 className="text-3xl font-bold">Featured Movies</h2>

      <div className="grid md:grid-cols-2 gap-8">
        {featuredMovies.map((movie) => (
          <Card key={movie.id} className="overflow-hidden group hover:shadow-xl transition-shadow">
            <Link href={`/movies/${movie.id}`}>
              <div className="relative">
                <Image
                  src={movie.backdrop || "/placeholder.svg"}
                  alt={movie.title}
                  width={600}
                  height={400}
                  className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex items-start gap-4">
                    <Image
                      src={movie.poster || "/placeholder.svg"}
                      alt={movie.title}
                      width={80}
                      height={120}
                      className="rounded-lg shadow-lg"
                    />
                    <div className="flex-1 text-white space-y-2">
                      <h3 className="text-xl font-bold">{movie.title}</h3>
                      <div className="flex items-center gap-3 text-sm">
                        <Badge className="bg-yellow-500 text-black">
                          <Star className="w-3 h-3 mr-1" />
                          {movie.rating}
                        </Badge>
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {movie.releaseDate}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Link>

            <CardContent className="p-6 space-y-4">
              <p className="text-muted-foreground">{movie.description}</p>
              <p className="text-sm font-medium">{movie.genre}</p>

              <div className="flex gap-3">
                <Button className="flex-1 bg-red-600 hover:bg-red-700" onClick={() => handleBookTickets(movie.id)}>
                  Book Tickets
                </Button>
                <Button variant="outline" size="icon" onClick={() => handleWatchTrailer(movie.id)}>
                  <Play className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}
