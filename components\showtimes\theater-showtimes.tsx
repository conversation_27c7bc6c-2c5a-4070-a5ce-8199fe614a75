"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Star, Wifi, Car, Coffee, Accessibility } from "lucide-react"
import { useRouter } from "next/navigation"

export function TheaterShowtimes() {
  const router = useRouter()

  const theaters = [
    {
      id: 1,
      name: "PVR Cinemas",
      location: "Phoenix Mall, Bangalore",
      distance: "2.5 km",
      rating: 4.2,
      amenities: ["Parking", "Food Court", "WiFi", "Wheelchair Access"],
      screens: [
        {
          id: 1,
          name: "Screen 1 - IMAX",
          format: "IMAX",
          showtimes: [
            { time: "10:30 AM", price: 350, available: true },
            { time: "2:15 PM", price: 400, available: true },
            { time: "6:00 PM", price: 450, available: false },
            { time: "9:45 PM", price: 400, available: true },
          ],
        },
        {
          id: 2,
          name: "Screen 3 - Regular",
          format: "2D",
          showtimes: [
            { time: "11:00 AM", price: 200, available: true },
            { time: "2:45 PM", price: 250, available: true },
            { time: "6:30 PM", price: 300, available: true },
            { time: "10:15 PM", price: 250, available: true },
          ],
        },
      ],
    },
    {
      id: 2,
      name: "INOX Multiplex",
      location: "Forum Mall, Bangalore",
      distance: "3.8 km",
      rating: 4.0,
      amenities: ["Parking", "Food Court", "Wheelchair Access"],
      screens: [
        {
          id: 3,
          name: "Screen 2 - 3D",
          format: "3D",
          showtimes: [
            { time: "11:30 AM", price: 280, available: true },
            { time: "3:15 PM", price: 320, available: true },
            { time: "7:00 PM", price: 380, available: true },
            { time: "10:45 PM", price: 320, available: false },
          ],
        },
      ],
    },
    {
      id: 3,
      name: "Cinepolis",
      location: "Orion Mall, Bangalore",
      distance: "5.2 km",
      rating: 4.3,
      amenities: ["Parking", "Food Court", "WiFi", "Premium Seating"],
      screens: [
        {
          id: 4,
          name: "Screen 1 - 4DX",
          format: "4DX",
          showtimes: [
            { time: "12:00 PM", price: 500, available: true },
            { time: "4:00 PM", price: 550, available: true },
            { time: "8:00 PM", price: 600, available: true },
          ],
        },
        {
          id: 5,
          name: "Screen 4 - Regular",
          format: "2D",
          showtimes: [
            { time: "10:45 AM", price: 180, available: true },
            { time: "2:30 PM", price: 220, available: true },
            { time: "6:15 PM", price: 280, available: true },
            { time: "10:00 PM", price: 220, available: true },
          ],
        },
      ],
    },
  ]

  const getAmenityIcon = (amenity: string) => {
    switch (amenity) {
      case "Parking":
        return <Car className="w-4 h-4" />
      case "Food Court":
        return <Coffee className="w-4 h-4" />
      case "WiFi":
        return <Wifi className="w-4 h-4" />
      case "Wheelchair Access":
        return <Accessibility className="w-4 h-4" />
      default:
        return null
    }
  }

  const handleShowtimeClick = (theaterId: number, screenId: number, time: string, available: boolean) => {
    if (!available) return

    // Navigate to seat selection with showtime details
    router.push(`/seat-selection?theater=${theaterId}&screen=${screenId}&time=${encodeURIComponent(time)}`)
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Available Showtimes</h2>

      <div className="space-y-6">
        {theaters.map((theater) => (
          <Card key={theater.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-xl">{theater.name}</CardTitle>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="w-4 h-4" />
                    <span>{theater.location}</span>
                    <span>•</span>
                    <span>{theater.distance}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-medium">{theater.rating}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {theater.amenities.map((amenity) => (
                        <div key={amenity} className="flex items-center gap-1 text-xs text-muted-foreground">
                          {getAmenityIcon(amenity)}
                          <span>{amenity}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {theater.screens.map((screen) => (
                <div key={screen.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <h4 className="font-semibold">{screen.name}</h4>
                      <Badge variant="outline">{screen.format}</Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {screen.showtimes.map((showtime, index) => (
                      <Button
                        key={index}
                        variant={showtime.available ? "outline" : "ghost"}
                        disabled={!showtime.available}
                        className={`flex flex-col h-auto py-3 ${
                          showtime.available
                            ? "hover:bg-red-50 hover:border-red-300 cursor-pointer"
                            : "opacity-50 cursor-not-allowed"
                        }`}
                        onClick={() => handleShowtimeClick(theater.id, screen.id, showtime.time, showtime.available)}
                      >
                        <span className="font-semibold">{showtime.time}</span>
                        <span className="text-sm text-muted-foreground">₹{showtime.price}</span>
                        {!showtime.available && <span className="text-xs text-red-500">Sold Out</span>}
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
