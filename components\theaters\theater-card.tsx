import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Clock, Car, Coffee, Wifi, Accessibility, Film } from "lucide-react"
import Image from "next/image"

interface Theater {
  id: number
  name: string
  location: string
  address: string
  distance: string
  rating: number
  totalReviews: number
  image: string
  amenities: string[]
  screens: number
  formats: string[]
  priceRange: string
  nextShow: string
}

interface TheaterCardProps {
  theater: Theater
}

export function TheaterCard({ theater }: TheaterCardProps) {
  const getAmenityIcon = (amenity: string) => {
    switch (amenity) {
      case "Parking":
        return <Car className="w-4 h-4" />
      case "Food Court":
        return <Coffee className="w-4 h-4" />
      case "WiFi":
        return <Wifi className="w-4 h-4" />
      case "Wheelchair Access":
        return <Accessibility className="w-4 h-4" />
      default:
        return <Film className="w-4 h-4" />
    }
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        <div className="grid md:grid-cols-3 gap-6">
          {/* Theater Image */}
          <div className="relative aspect-video md:aspect-square">
            <Image src={theater.image || "/placeholder.svg"} alt={theater.name} fill className="object-cover" />
          </div>

          {/* Theater Details */}
          <div className="md:col-span-2 p-6 space-y-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h3 className="text-xl font-bold">{theater.name}</h3>
                <div className="space-y-1">
                  <p className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="w-4 h-4" />
                    {theater.location}
                  </p>
                  <p className="text-sm text-muted-foreground ml-6">{theater.address}</p>
                  <p className="text-sm font-medium text-red-600 ml-6">{theater.distance} away</p>
                </div>
              </div>

              <div className="text-right">
                <div className="flex items-center gap-1 mb-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="font-semibold">{theater.rating}</span>
                </div>
                <p className="text-xs text-muted-foreground">({theater.totalReviews} reviews)</p>
              </div>
            </div>

            {/* Theater Info */}
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div>
                  <span className="font-medium">Screens:</span>
                  <span className="ml-2">{theater.screens}</span>
                </div>
                <div>
                  <span className="font-medium">Price Range:</span>
                  <span className="ml-2">{theater.priceRange}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="font-medium">Next Show:</span>
                  <span className="ml-2 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {theater.nextShow}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Formats:</span>
                  <div className="flex gap-1 mt-1">
                    {theater.formats.map((format) => (
                      <Badge key={format} variant="outline" className="text-xs">
                        {format}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div>
              <span className="font-medium text-sm">Amenities:</span>
              <div className="flex flex-wrap gap-2 mt-2">
                {theater.amenities.map((amenity) => (
                  <div key={amenity} className="flex items-center gap-1 text-xs text-muted-foreground">
                    {getAmenityIcon(amenity)}
                    <span>{amenity}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-2">
              <Button className="bg-red-600 hover:bg-red-700">View Showtimes</Button>
              <Button variant="outline">Get Directions</Button>
              <Button variant="outline">Call Theater</Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
