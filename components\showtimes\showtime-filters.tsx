import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

export function ShowtimeFilters() {
  const formats = ["2D", "3D", "IMAX", "4DX", "Dolby Atmos"]
  const languages = ["English", "Hindi", "Tamil", "Telugu"]
  const times = ["Morning", "Afternoon", "Evening", "Night"]

  const activeFilters = ["IMAX", "English", "Evening"]

  return (
    <div className="space-y-6">
      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm font-medium">Active filters:</span>
          {activeFilters.map((filter) => (
            <Badge key={filter} variant="secondary" className="flex items-center gap-1">
              {filter}
              <X className="w-3 h-3 cursor-pointer" />
            </Badge>
          ))}
          <Button variant="ghost" size="sm" className="text-red-600">
            Clear all
          </Button>
        </div>
      )}

      {/* Filter Categories */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="space-y-3">
          <h3 className="font-semibold">Format</h3>
          <div className="flex flex-wrap gap-2">
            {formats.map((format) => (
              <Button
                key={format}
                variant={activeFilters.includes(format) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(format) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {format}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold">Language</h3>
          <div className="flex flex-wrap gap-2">
            {languages.map((language) => (
              <Button
                key={language}
                variant={activeFilters.includes(language) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(language) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {language}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold">Show Time</h3>
          <div className="flex flex-wrap gap-2">
            {times.map((time) => (
              <Button
                key={time}
                variant={activeFilters.includes(time) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(time) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {time}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
