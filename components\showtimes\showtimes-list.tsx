"use client"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin } from "lucide-react"

interface ShowtimesListProps {
  movieId: string
}

export function ShowtimesList({ movieId }: ShowtimesListProps) {
  const showtimes = [
    {
      theater: "PVR Cinemas - Phoenix Mall",
      location: "Bangalore",
      times: [
        { time: "10:30 AM", format: "2D", price: 200 },
        { time: "2:15 PM", format: "3D", price: 280 },
        { time: "6:00 PM", format: "IMAX", price: 400 },
        { time: "9:45 PM", format: "2D", price: 250 },
      ],
    },
    {
      theater: "INOX - Forum Mall",
      location: "Bangalore",
      times: [
        { time: "11:00 AM", format: "2D", price: 180 },
        { time: "2:45 PM", format: "3D", price: 260 },
        { time: "6:30 PM", format: "2D", price: 220 },
        { time: "10:15 PM", format: "3D", price: 280 },
      ],
    },
  ]

  return (
    <section className="space-y-6">
      <h2 className="text-3xl font-bold">Book Tickets</h2>

      <div className="space-y-4">
        {showtimes.map((theater, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5 text-muted-foreground" />
                <div>
                  <CardTitle className="text-lg">{theater.theater}</CardTitle>
                  <p className="text-sm text-muted-foreground">{theater.location}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {theater.times.map((showtime, timeIndex) => (
                  <Button
                    key={timeIndex}
                    variant="outline"
                    className="flex flex-col h-auto py-3 hover:bg-red-50 hover:border-red-300 transition-all cursor-pointer"
                    onClick={() => {
                      // Navigate to seat selection with showtime details
                      const params = new URLSearchParams({
                        movieId: movieId,
                        theater: theater.theater,
                        location: theater.location,
                        time: showtime.time,
                        format: showtime.format,
                        price: showtime.price.toString(),
                        date: "Today, Dec 25", // You can make this dynamic
                      })
                      window.location.href = `/seat-selection?${params.toString()}`
                    }}
                  >
                    <span className="font-semibold">{showtime.time}</span>
                    <Badge variant="secondary" className="mt-1 mb-1">
                      {showtime.format}
                    </Badge>
                    <span className="text-sm text-muted-foreground">₹{showtime.price}</span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}
