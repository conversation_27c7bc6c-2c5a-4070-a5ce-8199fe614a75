import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

export function MovieFilters() {
  const genres = ["Action", "Comedy", "Drama", "Horror", "Sci-Fi", "Romance", "Thriller", "Adventure"]

  const languages = ["English", "Hindi", "Tamil", "Telugu", "Malayalam"]

  const activeFilters = ["Action", "English"]

  return (
    <div className="space-y-6">
      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm font-medium">Active filters:</span>
          {activeFilters.map((filter) => (
            <Badge key={filter} variant="secondary" className="flex items-center gap-1">
              {filter}
              <X className="w-3 h-3 cursor-pointer" />
            </Badge>
          ))}
          <Button variant="ghost" size="sm" className="text-red-600">
            Clear all
          </Button>
        </div>
      )}

      {/* Filter Categories */}
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <h3 className="font-semibold">Genres</h3>
          <div className="flex flex-wrap gap-2">
            {genres.map((genre) => (
              <Button
                key={genre}
                variant={activeFilters.includes(genre) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(genre) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {genre}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold">Languages</h3>
          <div className="flex flex-wrap gap-2">
            {languages.map((language) => (
              <Button
                key={language}
                variant={activeFilters.includes(language) ? "default" : "outline"}
                size="sm"
                className={activeFilters.includes(language) ? "bg-red-600 hover:bg-red-700" : ""}
              >
                {language}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
