import { MovieDetails } from "@/components/movies/movie-details"
import { MovieTrailer } from "@/components/movies/movie-trailer"
import { MovieCast } from "@/components/movies/movie-cast"
import { MovieReviews } from "@/components/movies/movie-reviews"
import { ShowtimesList } from "@/components/showtimes/showtimes-list"
import { RelatedMovies } from "@/components/movies/related-movies"

interface MovieDetailsPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function MovieDetailsPage({ params }: MovieDetailsPageProps) {
  const { id } = await params

  return (
    <div className="min-h-screen">
      <MovieDetails movieId={id} />
      <div className="container mx-auto px-4 py-8 space-y-12">
        <MovieTrailer />
        <ShowtimesList movieId={id} />
        <MovieCast />
        <MovieReviews />
        <RelatedMovies />
      </div>
    </div>
  )
}
