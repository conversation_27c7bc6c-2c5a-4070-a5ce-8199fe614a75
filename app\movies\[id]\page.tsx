import { MovieDetails } from "@/components/movies/movie-details"
import { MovieTrailer } from "@/components/movies/movie-trailer"
import { MovieCast } from "@/components/movies/movie-cast"
import { MovieReviews } from "@/components/movies/movie-reviews"
import { ShowtimesList } from "@/components/showtimes/showtimes-list"
import { RelatedMovies } from "@/components/movies/related-movies"

interface MovieDetailsPageProps {
  params: {
    id: string
  }
}

export default function MovieDetailsPage({ params }: MovieDetailsPageProps) {
  return (
    <div className="min-h-screen">
      <MovieDetails movieId={params.id} />
      <div className="container mx-auto px-4 py-8 space-y-12">
        <MovieTrailer />
        <ShowtimesList movieId={params.id} />
        <MovieCast />
        <MovieReviews />
        <RelatedMovies />
      </div>
    </div>
  )
}
