import { SeatMap } from "@/components/seats/seat-map"
import { SeatLegend } from "@/components/seats/seat-legend"
import { BookingSummary } from "@/components/booking/booking-summary"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function SeatSelectionPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Select Your Seats</h1>
          <p className="text-muted-foreground">Choose your preferred seats for the best movie experience</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Screen</CardTitle>
                <div className="h-2 bg-gradient-to-r from-gray-300 via-gray-600 to-gray-300 rounded-full mx-8" />
              </CardHeader>
              <CardContent>
                <SeatMap />
              </CardContent>
            </Card>

            <SeatLegend />
          </div>

          <div>
            <BookingSummary />
          </div>
        </div>
      </div>
    </div>
  )
}
