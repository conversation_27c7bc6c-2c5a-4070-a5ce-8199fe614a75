"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Play, Calendar, MapPin } from "lucide-react"
import Image from "next/image"
import { useRouter } from "next/navigation"

export function HeroSection() {
  const router = useRouter()

  const handleBookNow = () => {
    // Navigate to the specific movie's showtimes
    router.push("/movies/1") // The Dark Knight movie page
  }

  const handleWatchTrailer = () => {
    // You can implement trailer modal or navigate to trailer
    console.log("Opening trailer...")
  }

  return (
    <div className="relative h-[70vh] bg-gradient-to-r from-black/80 to-black/40 overflow-hidden">
      <Image src="/placeholder.svg?height=800&width=1200" alt="Featured Movie" fill className="object-cover -z-10" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-transparent" />

      <div className="container mx-auto px-4 h-full flex items-center">
        <div className="max-w-2xl text-white space-y-6">
          <div className="space-y-2">
            <p className="text-red-400 font-semibold">NOW PLAYING</p>
            <h1 className="text-5xl md:text-6xl font-bold leading-tight">The Dark Knight</h1>
            <p className="text-xl text-gray-300">
              When the menace known as the Joker wreaks havoc and chaos on the people of Gotham, Batman must accept one
              of the greatest psychological and physical tests.
            </p>
          </div>

          <div className="flex items-center gap-6 text-sm">
            <span className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              2h 32min
            </span>
            <span className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Action, Crime, Drama
            </span>
            <span className="bg-yellow-500 text-black px-2 py-1 rounded font-bold">9.0 IMDb</span>
          </div>

          <div className="flex gap-4">
            <Button size="lg" className="bg-red-600 hover:bg-red-700" onClick={handleWatchTrailer}>
              <Play className="w-5 h-5 mr-2" />
              Watch Trailer
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-black"
              onClick={handleBookNow}
            >
              Book Now
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
