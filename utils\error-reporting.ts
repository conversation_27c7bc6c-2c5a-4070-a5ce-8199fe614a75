interface ErrorReport {
  message: string
  stack?: string
  url: string
  userAgent: string
  timestamp: number
  userId?: string
}

export function reportError(error: Error, context?: Record<string, any>) {
  const errorReport: ErrorReport = {
    message: error.message,
    stack: error.stack,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: Date.now(),
    ...context,
  }

  // Send to error reporting service (e.g., Sentry)
  if (process.env.NODE_ENV === "production") {
    fetch("/api/errors", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(errorReport),
    }).catch((err) => {
      console.error("Failed to report error:", err)
    })
  }

  console.error("Error reported:", errorReport)
}

// Global error handler
if (typeof window !== "undefined") {
  window.addEventListener("error", (event) => {
    reportError(new Error(event.message), {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    })
  })

  window.addEventListener("unhandledrejection", (event) => {
    reportError(new Error(event.reason), {
      type: "unhandledrejection",
    })
  })
}
