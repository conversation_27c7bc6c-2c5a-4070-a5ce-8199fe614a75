# Cinema Booking System - Frontend

A modern, responsive frontend for a comprehensive Cinema Booking System built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Authentication System**: JWT-based authentication with token refresh
- **Movie Management**: Browse, search, and filter movies
- **Theater Management**: View theaters and seat layouts
- **Showtime Management**: Browse showtimes by movie, theater, or date
- **Seat Selection**: Interactive seat selection with real-time availability
- **Booking System**: Complete booking flow with confirmation
- **User Dashboard**: Profile management and booking history
- **Responsive Design**: Mobile-first approach with touch-friendly interface

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + shadcn/ui
- **HTTP Client**: Axios with interceptors
- **State Management**: React Context API
- **Form Handling**: React Hook Form with Zod validation
- **Date Handling**: date-fns
- **Icons**: Lucide React

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages
├── components/            # Reusable UI components
│   ├── auth/             # Authentication components
│   ├── booking/          # Booking flow components
│   ├── common/           # Shared components
│   ├── movies/           # Movie-related components
│   ├── seats/            # Seat selection components
│   ├── showtimes/        # Showtime components
│   ├── theaters/         # Theater components
│   ├── ui/               # Base UI components (shadcn/ui)
│   └── user/             # User dashboard components
├── context/              # React Context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── services/             # API service layer
├── types/                # TypeScript type definitions
├── utils/                # Utility functions and constants
└── styles/               # Global styles
```

## 🔧 Setup & Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env.local` file:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8080/api
   NEXT_PUBLIC_APP_NAME=CinemaBook
   NEXT_PUBLIC_APP_VERSION=1.0.0
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔌 Backend Integration

This frontend is designed to work with a Spring Boot backend API. The backend should be running on `http://localhost:8080` with the following endpoints:

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh-token` - Token refresh
- `GET /api/auth/validate-token` - Token validation

### Movies
- `GET /api/movies` - Get paginated movies
- `GET /api/movies/{id}` - Get movie by ID
- `GET /api/movies/search` - Search movies
- `GET /api/movies/featured` - Get featured movies
- `GET /api/movies/now-playing` - Get currently playing movies

### Theaters & Showtimes
- `GET /api/theaters` - Get all theaters
- `GET /api/showtimes` - Get showtimes with filters
- `GET /api/seats/theater/{id}` - Get theater seats

### Bookings
- `POST /api/bookings` - Create booking
- `GET /api/bookings/user` - Get user bookings
- `PUT /api/bookings/{id}/cancel` - Cancel booking

## 🎨 UI Components

The project uses shadcn/ui components built on top of Radix UI primitives:

- **Forms**: Input, Label, Button, Checkbox, Select
- **Navigation**: Navbar, Breadcrumb, Pagination
- **Feedback**: Toast, Alert, Loading Spinner
- **Layout**: Card, Separator, Tabs, Dialog
- **Data Display**: Table, Badge, Avatar

## 🔐 Authentication Flow

1. User logs in with email/password
2. Backend returns JWT access token (24h) and refresh token (7d)
3. Tokens are stored in localStorage
4. API requests include Authorization header
5. Automatic token refresh on 401 responses
6. Redirect to login on refresh failure

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Touch Friendly**: Large touch targets for mobile interaction
- **Progressive Enhancement**: Enhanced features for larger screens

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

3. **Deploy to Vercel** (recommended)
   ```bash
   npx vercel
   ```

## 🧪 Development

- **Type Checking**: `npm run type-check`
- **Linting**: `npm run lint`
- **Building**: `npm run build`

## 📄 License

This project is licensed under the MIT License.
